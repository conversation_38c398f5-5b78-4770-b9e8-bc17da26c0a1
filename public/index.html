<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit"/>
    <meta name="force-rendering" content="webkit"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
      <link rel="stylesheet" href="../public/index.css">
    <title>唐山市教育入学一件事服务平台管理后台</title>
    <script src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
  </head>
  <body>
    <div id="app"></div>
  </body>
	<% if (process.env.NODE_ENV == "production") { %>
		<script>
			window.console = (function () {
				var c = {};
				c.log = c.warn = c.debug = c.info = c.error = c.time = c.dir = c.profile = c.clear = c.exception = c.trace = c.assert = function () { };
				return c;
			})();
		</script>
	<% } %>
	<script>
		window.onload = function () {
			let scripts = document.getElementsByTagName('script')
			let xhr = new XMLHttpRequest()
			for(let i = 0; i < scripts.length; i++) {
				let curScript = scripts[i]
				if (curScript.src.indexOf('app') > -1) {
					xhr.open('GET', `${ scripts[i].src }?ts=${ new Date().getTime() }`, false)
					xhr.onreadystatechange = function () {
						if (xhr.readyState == 4) {
							if (xhr.status >= 200 && xhr.status < 300) {
								if (xhr.responseText && xhr.responseText.indexOf('<!DOCTYPE html>') == 0) {
									location.reload()
								}
							}
						}
					}
					xhr.send()
					break
				}
			}
		}
	</script>
</html>
