<template>
  <div>
    <!--        v-for="item in deptOptions"-->
    <!--        :label="item.deptName"-->
    <!--        :name="item.deptCode"-->
    <!--        :key="item.id"-->
    <!--        style="overflow: scroll;height:800px"-->
    <div>
      <el-input style="width: 200px;margin:0 20px  20px 0" v-model="search.keywords" placeholder="姓名/身份证号" clearable></el-input>
      <el-button type="primary" @click="getTable">搜索</el-button>
      <div style="margin-bottom: 20px">
        <el-button
            size="small"
            type="primary"
            icon="el-icon-s-promotion"
            v-if="role == 'COUNTY_ADMIN'"
            @click="gongShiLuQuJieGuo"
        >公示录取结果</el-button
        >

        <el-button
            size="small"
            type="primary"
            icon="el-icon-s-promotion"
            v-if="role == 'SCHOOL'"
            @click="faSongLuQu"
        >发送录取通知书</el-button
        >
      </div>
    </div>
    <el-table
        :data="tableData.records"
        border
        stripe
        v-loading="tableLoading"
    >
      <el-table-column
          align="center"
          label="序号"
          prop="index"
          width="80"
      >
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="报名ID"
          prop="signId"
          width="140"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
          width="120"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="idCard"
          width="170"
      ></el-table-column>
      <el-table-column
          align="center"
          label="多胞胎数量"
          prop="tiwnsNum"
          width="85"
      ></el-table-column>
      <el-table-column
          align="center"
          label="原报名学校"
          prop="fromSchoolName"
          width="180"
      >
      </el-table-column>

      <el-table-column
          align="center"
          label="原报名区县"
          prop="fromDeptName"
          width="180"
      ></el-table-column>
      <!--          <el-table-column-->
      <!--            align="center"-->
      <!--            label="调剂状态"-->
      <!--            prop=""-->
      <!--            width="180"-->
      <!--        ></el-table-column>-->
      <!--          <el-table-column-->
      <!--              align="center"-->
      <!--              label="调剂区县"-->
      <!--              prop=""-->
      <!--              width="180"-->
      <!--          ></el-table-column>-->
      <el-table-column
          align="center"
          label="调入学校"
          prop="toSchoolName"
          width="180"
      ></el-table-column>
      <!--          <el-table-column-->
      <!--              align="center"-->
      <!--              label="是否公示"-->
      <!--              prop="publicStatus"-->
      <!--          >-->
      <!--            <template slot-scope="scope">-->
      <!--              <template>{{scope.row.publicStatus==0?'未公布':scope.row.publicStatus==1?'公布':''}}</template>-->
      <!--            </template>-->
      <!--          </el-table-column>-->
      <el-table-column
          align="center"
          label="是否报到"
          prop="signStatus"
      >
        <template slot-scope="scope">
          <template>{{scope.row.signStatus==0?'待报到':scope.row.signStatus==1?'报到':scope.row.signStatus==2?'未报到':''}}</template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="未报到原因" prop="signContent"></el-table-column>
      <el-table-column
          align="center"
          label="调剂原因"
          prop="content"
          width="180"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="360" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row, $index)"
              v-if="row.isFive==0"
          >详情</el-link
          >
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail1(row)"
              v-if="row.isFive==1"
          >详情</el-link
          >
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              v-if="role=='COUNTY_ADMIN'"
              @click="buChongCaiLiao(row)"
          >补充材料</el-link
          >
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              v-if="role=='COUNTY_ADMIN'"
              @click="liuZhuan(row)"
          >流转记录</el-link
          >
          <el-link
              icon="el-icon-refresh"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              v-if="role=='COUNTY_ADMIN'"
              @click="adjust(row)"
          >调出</el-link
          >
          <el-link
              icon="el-icon-check"
              type="warning"
              :underline="false"
              @click="report(row)"
              v-if="role == 'SCHOOL'"
          >报到</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
    </div>
    <!--流转记录-->
    <el-dialog
        title="流转记录"
        :visible.sync="liuZhuanShow"
        width="60%"
    >
      <el-table
          :data="liuZhuanData"
          style="width: 100%"
      >
        <el-table-column
            align="center"
            label="报名ID"
            prop="signId"
            width="140"
        ></el-table-column>
        <el-table-column
            align="center"
            label="学生姓名"
            prop="studentName"
            width="120"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="身份证号"
            prop="idCard"
            width="170"
        ></el-table-column>
        <el-table-column
            align="center"
            label="多胞胎数量"
            prop="tiwnsNum"
            width="85"
        ></el-table-column>
        <el-table-column
            align="center"
            label="原报名学校"
            prop="fromSchoolName"
            width="180"
        >
        </el-table-column>

        <el-table-column
            align="center"
            label="原报名区县"
            prop="fromDeptName"
            width="180"
        ></el-table-column>
        <!--          <el-table-column-->
        <!--            align="center"-->
        <!--            label="调剂状态"-->
        <!--            prop=""-->
        <!--            width="180"-->
        <!--        ></el-table-column>-->
        <!--          <el-table-column-->
        <!--              align="center"-->
        <!--              label="调剂区县"-->
        <!--              prop=""-->
        <!--              width="180"-->
        <!--          ></el-table-column>-->
        <el-table-column
            align="center"
            label="调入学校"
            prop="toSchoolName"
            width="180"
        ></el-table-column>
        <el-table-column
            align="center"
            label="调剂原因"
            prop="content"
            width="180"
        ></el-table-column>
      </el-table>
      <div class="page-container" v-if="total > 0">
        <el-pagination
            background
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page.sync="liuZhuanForm.pageNumber"
            layout="total, prev, pager, next, sizes"
            :page-sizes="$pageSizes"
            :total="liuZhuanTotal"
        >
        </el-pagination>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
            <el-button @click="liuZhuanShow = false">取 消</el-button>
          </span>
    </el-dialog>
    <!--补充材料-->
    <el-dialog
        title="补充材料"
        :visible.sync="buChongCaiLiaoShow"
        width="60%"
    >
      <uploadPicMutiple ref="mediaUpload"/>
      <span
          slot="footer"
          class="dialog-footer"
      >
            <el-button @click="buChongCaiLiaoShow = false">取 消</el-button>
            <el-button
                type="primary"
                @click="addBuChongCaiLiao"
            >确 定</el-button>
          </span>
    </el-dialog>
    <!-- 调剂 -->
    <el-dialog
        title="调剂"
        :visible.sync="modal.adjust"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="adjustForm"
          ref="adjustForm"
          :rules="adjustRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item label="报名ID：">{{ adjustForm.signId}}</el-form-item>
        <el-form-item label="学生姓名：">{{adjustForm.studentName }}</el-form-item>
        <el-form-item label="身份证号：">{{adjustForm.idCard }}</el-form-item>
        <!--            <el-form-item label="报名学校：">{{-->
        <!--                adjustInfo.enrollSchoolName-->
        <!--              }}</el-form-item>-->
        <!--            <el-form-item prop="adjustQuXianId" label="调剂区县选择：">-->
        <!--              <el-select-->
        <!--                  v-model="adjustForm.adjustQuXianId"-->
        <!--                  style="width: 300px"-->
        <!--                  @change="adjustSchoolChange"-->
        <!--              >-->
        <!--                <el-option-->
        <!--                    v-for="item in quXianListAvailable"-->
        <!--                    :label="item.deptName"-->
        <!--                    :value="item.id"-->
        <!--                    :key="item.id"-->
        <!--                ></el-option>-->
        <!--              </el-select>-->
        <!--            </el-form-item>-->
        <el-form-item prop="schoolId" label="调剂学校选择：">
          <el-select
              v-model="adjustForm.schoolId"
              style="width: 300px"
              @change="change"
          >
            <el-option
                v-for="item in schoolListAvailable"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="调剂原因：">
          <el-input @input="change" type="textarea" style="width:300px" placeholder="请输入调剂原因" v-model="adjustForm.content"></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('adjust', false)"
        >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmAdjust"
        >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 标准版详情 -->
    <el-dialog
        :visible.sync="modal.stuDetail"
        :close-on-click-modal="false"
        title="学生报名详情"
        center
        width="1240px"
        @close="stuDetailClose"
    >
      <enroll-detail-ji-ze
          v-if="deptCode1=='130431'&&period==3"
          :stu-detail="curStuDetail"
          :deptCode="deptCode1"
          :key="curStuDetail.studentBaseId"
      >

      </enroll-detail-ji-ze>
      <enroll-detail-qiu-xian
          v-if="deptCode1=='130430'&&period==3"
          :stu-detail="curStuDetail"
          :deptCode="deptCode1"
          :key="curStuDetail.studentBaseId"
      >

      </enroll-detail-qiu-xian>
      <enroll-detail
          v-if="deptCode1!='130430'&&deptCode1!='130431'"
          :stu-detail="curStuDetail"
          :deptCode="deptCode1"
          :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <div class="flex-center" style="margin-top: 20px">
        <!--        <el-button type="primary" @click="nextEnrollDetail" size="small"-->
        <!--          >下一条</el-button-->
        <!--        >-->

        <el-button size="small" type="info" @click="stuDetailClose"
        >关闭</el-button
        >
      </div>
    </el-dialog>

    <!--市五区详情-->
    <el-dialog
        title="学生报名信息详情"
        width="800px"
        append-to-body
        :show-close="false"
        :visible.sync="modal.detail"
        :close-on-click-modal="false"
        center
    >
      <div>
        <component
            v-if="modal.detail"
            :ref="'detail'"
            :is="'shiWuQuDetail'"
            :row="row"
            :data=null
            :mode=null
            :data-row="selectRow?selectRow:{}"
            :deptCode="deptCode1"
            @searchs="search"

        />
        <!--        @save-complete="dialogFormComponent.saveComplete"-->
      </div>
      <div class="flex-center" style="margin-top:20px ">
        <el-button
            type="default"
            @click="fanHui"
            size="small"
        >返回</el-button
        >
        <!--        <el-button-->
        <!--            type="primary"-->
        <!--            @click="xiaYiTiao"-->
        <!--            :loading="saveLoading"-->
        <!--            size="small"-->
        <!--        >下一条</el-button-->
        <!--        >-->
        <!--        <el-button-->
        <!--            type="primary"-->
        <!--            @click="tiaoJi"-->
        <!--            :loading="saveLoading"-->
        <!--            size="small"-->
        <!--            v-if="role == 'COUNTY_ADMIN' "-->
        <!--        >调剂</el-button-->
        <!--        >-->
        <!--        || role == 'AUDITOR'-->
      </div>

    </el-dialog>
    <!-- 报到 -->
    <el-dialog
        title="报到"
        :visible.sync="baoDaoShow"
        align="center"
        width="500px"
    >
      <div>
        <el-radio-group v-model="baodao.signStatus">
          <el-radio :label="2">未报到</el-radio>
          <el-radio :label="1">报到</el-radio>
        </el-radio-group>
        <!--         <el-button type="primary" @click="xiuGaiXinXi">驳回-修改信息</el-button>-->
        <!--         <el-button type="info" @click="buKeZaiBao">驳回-驳回-不可再报</el-button>-->
        <el-input v-if="baodao.signStatus==2" type="textarea" placeholder="请输入未报到原因" v-model="baodao.content"  style="margin-top: 20px"></el-input>
      </div>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="quXiao">取 消</el-button>
        <el-button
            type="primary"
            @click="baoDao"
        >确 定</el-button>
      </div>
    </el-dialog>
    <!-- 发送录取通知书-->
    <el-dialog
        title="发送录取通知书"
        :visible.sync="faSongLuQuShow"
        width="500px"
    >
      <el-input type="textarea" placeholder="请输入录取内容" v-model="luQuTongZhi.content" @input="change"></el-input>
      <span
          slot="footer"
          class="dialog-footer"
      >
      <el-button @click="quXiao">取 消</el-button>
      <el-button
          type="primary"
          @click="faSongLuQuJieGuo"
      >确 定</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {adjust, getTypeList} from "@/api/enrollment";
import {getDepts} from "@/api/common";
import EnrollDetail from "@/components/biaoZhunBanXiaoXueStudentList/EnrollDetail";
import EnrollDetailJiZe from "@/components/EnrollDetailJiZe";
import EnrollDetailQiuXian from "@/components/EnrollDetailQiuXian";
// import { getDepts } from "@/api/common";
import uploadPicMutiple from "@/components/UploadPicMutiple";
import {
  getKuaQuList,
  kuaQuJiLu,
  postKuaQuBuChong,
  getKuaQuBuChong,
  sendLuQu,
  gongShi,
  baoDao1,
  quXianTiaoJiao
} from "@/api/tiaoChu";
export default {
  name: "index",
  mixins: [TableMixin,ModalMixin],
  components: { EnrollDetail,uploadPicMutiple,EnrollDetailJiZe,EnrollDetailQiuXian },
  data() {
    return {
      faSongLuQuShow:false,
      luQuTongZhi:{},
      selectRow:{},
      row:'',
      deptCode: this.$store.getters.deptCode,
      deptCode1:'',
      role:this.$store.getters.role,
      prefixDeptCode: "0", // 当前选项卡区县
      search: {
        keywords: "",
        type:2,
        schoolType:2,
        // toSchoolId:this.$store.getters.role=='SCHOOL'?this.$store.getters.deptId:null,
        toDeptId:this.$store.getters.role!='SCHOOL'?this.$store.getters.deptId:null
      },
      baodao:{
        signStatus:'',
      },
      baoDaoShow:false,
      tableLoading: false,
      dialogTitle: "",
      form: {
        studentId: "",
      },
      deptOptions: [],
      rules: {
        idNumber: [
          {required: true, message: "请输入学生身份证号", trigger: "change"},
        ],
      },
      submitLoading: false,
      submitBatchLoading: false,
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
      tableData: {
      },
      total:0,
      period:'2',
      quXianListAvailable: [],
      schoolListAvailable: [],
      // 调剂
      adjustForm: {
        studentId:'',
        id: "",
        adjustSchoolId: "",
        adjustSchoolName: "",
        adjustYuanYin: ''
      },
      adjustRules: {
        // adjustQuXianId: [{
        //   required: true,
        //   message: "请选择调剂区县",
        //   trigger: "change",
        // },],
        schoolId: [
          {
            required: true,
            message: "请选择调剂学校",
            trigger: "change",
          },
        ],
        content: [{
          required: true,
          message: "请输入调剂原因",
          trigger: "blur",
        },
          {required: true, min: 2, max: 500, message: '请输入2-500个文字', trigger: 'blur'}
        ]
      },
      modal: {
        reject: false,
        adjust: false,
        report: false,
        notice: false,
        priority: false,
        stuDetail: false,
        detail:false
      },
      adjustInfo: {
        enRollId: "",
        studentName: "",
        enrollSchoolName: "",
        adjustSchoolName: "",
      },
      // 当前学生报名详情
      curStuDetail: {},
      buChongCaiLiaoShow: false,
      liuZhuanShow: false,
      liuZhuanForm:{
        pageSize:10,
        pageNumber:1,
        deptId:''
      },
      liuZhuanData:[],
      liuZhuanTotal:0
    };
  },
  created() {
    this.getTableData()
    // console.log(this.role,"role")
    if(this.role!='SCHOOL'){
      this.getSchoolList1()
    }

  },
  methods: {
    change(){
      this.$forceUpdate()
    },
    //发送录取通知书
    faSongLuQu(){
      this.faSongLuQuShow=true
      // this.luQuTongZhi.content=""
    },
    faSongLuQuJieGuo(){
      this.$confirm('确认要为所有学生发送录取通知', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sendLuQu(this.luQuTongZhi).then(res => {
          if (res) {
            this.$message({
              type: 'success',
              message: '发送成功!'
            })
            this.getTableData()
            this.luQuTongZhi = {
              content: "",
            }
            this.faSongLuQuShow = false
          }
        })
      })
    },
    //公示录取结果
    gongShiLuQuJieGuo(){
      this.$confirm('确定要公示所有学生录取结果', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        gongShi({}).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '公布录取结果成功!'
            });
            // this.idList=[]
            this.getTableData()
          }
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    // 报到
    report(row) {
      console.log(row,"row")
      this.baodao.studentId=row.studentId
      this.baodao.deptId=row.deptId
      this.baodao.signStatus=row.signStatus+''
      this.baoDaoShow=true

    },
    baoDao(){
      this.$confirm('确认该学生'+`${this.baodao.signStatus==2?'未报到':'报到'}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        baoDao1(this.baodao,this.prefixDeptCode).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.baodao={}
          }
          this.baoDaoShow=false
          this.getTableData()
        })


      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消'
        });
      });
    },
    //取消
    quXiao(){
      this.baoDaoShow=false
      this.faSongLuQuShow=false
      this.getTableData()
    },
    buChongCaiLiao(row){
      this.buChongCaiLiaoShow=true
      this.form.studentId=row.studentId
      this.form.deptId=row.deptId
      this.form.schoolId=row.toSchoolId
      // setTimeout(() => {
      //   console.log( this.$refs.mediaUpload.upload)
      //   this.$refs.mediaUpload.upload.list=[]
      setTimeout(() => {
        getKuaQuBuChong({studentId: row.studentId,deptId: row.deptId,schoolId:row.toSchoolId}).then(res=>{
          if(res.material!=null){
            this.$refs.mediaUpload.upload.list=res.material.split(',')
          }else{
            this.$refs.mediaUpload.upload.list=[]
          }
          console.log(res,"res")
          // this.$refs.mediaUpload.upload.list=res.material!=''?res.material.split(','):[]


        })
      }, 0);
      // }, 0);


    },
    addBuChongCaiLiao(){


      setTimeout(() => {
        this.form.material = "";
        console.log(this.$refs.mediaUpload.upload,"list");
        this.form.material =
            this.$refs.mediaUpload.upload.list.join(",");
        postKuaQuBuChong(this.form).then(res=>{
          if(res){
            this.$message.success("上传成功")
            this.buChongCaiLiaoShow=false
          }
          console.log(res,"res")
        })
      }, 0);


    },
    // 初次查询
    getTable(){
      this.search.pageNumber=1
      this.getTableData()
    },
    async getTableData() {
      getKuaQuList(this.search).then((res) =>{
        console.log(res,"res")
        this.tableData = res;
        this.total=Number(res.total)
      })
      // if (this.prefixDeptCode != "0") {
      //   // this.getList();
      // } else {
      // let depts = await getDepts({ level: 2 });
      // this.deptOptions =depts.filter((item) => item.deptCode!='130426');
      // this.prefixDeptCode = this.deptOptions[0].deptCode;
      // this.getList();
      // }
    },
    getList(){},
    // 获取报名类别 2小学 3初中
    getTypeList() {
      getTypeList({ key: 3 }, this.prefixDeptCode).then((res) => {
        this.typeList = res;
      });
    },
    // 查询学校
    getDepts() {
      let params = {
        level: 3,
        period: "3",
        parentId: this.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 调剂
    adjust(row) {
      console.log(row, "row")
      this.switchModal("adjust", true);
      // this.$nextTick(() => {
      //   if(this.$refs.adjustForm){
      //     this.$refs.adjustForm.resetFields();
      //   }
      // this.adjustForm.toSchoolId=''
      // this.adjustForm.content=''
      this.adjustForm.signId=row.signId
      this.adjustForm.studentId = row.studentId;
      this.adjustForm.studentName = row.studentName;
      this.adjustForm.fromDeptName = row.fromDeptName;
      this.adjustForm.deptId=row.deptId
      this.adjustForm.idCard = row.idCard;
      this.period=row.period
      this.getSchoolList1()
      // this.adjustForm.adjustSchoolName = "";

      // this.adjustInfo.enrollId = row.enrollId;
      // this.adjustInfo.studentName = row.studentName;
      // this.adjustInfo.enrollSchoolName = row.enrollSchoolName;
      // this.adjustInfo.adjustSchoolName = row.adjustSchoolName;
      // });
    },
    // 调剂确定
    confirmAdjust() {
      this.$refs.adjustForm.validate((valid) => {
        if (valid) {
          quXianTiaoJiao(this.adjustForm, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.adjustForm.schoolId=''
            this.adjustForm.content=''
            this.switchModal("adjust", false);
            this.getTableData();
          });
        }
      });
    },
    async getQuXianList(){
      let depts = await getDepts({ level: 2 });
      this.quXianListAvailable=depts.filter((item) => item.deptCode!='130426');
    },
    getSchoolList1(item){
      // console.log(item,"getters")
      getDepts({type:1,period:this.period,level:3,parentId:this.$store.getters.deptId},this.prefixDeptCode).then(res=>{
        this.schoolListAvailable=res
        console.log(res,"res")
      })
    },
    // 调剂学校选择change
    adjustSchoolChange() {
      this.schoolList.forEach((item) => {
        if (this.adjustForm.adjustSchoolId == item.id) {
          this.adjustForm.adjustSchoolName = item.deptName;
        }
      });
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.deptCode1=row.deptCode
      this.index = index;
      this.modal.stuDetail = true;
    },
    detail1(row){
      console.log(row,"row")
      this.row=row
      this.period=row.period
      this.deptCode1=row.deptCode
      this.switchModal('detail', true)
    },
    fanHui(){
      this.$refs.detail.fanHui()
      this.switchModal('detail', false)
    },
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
    liuZhuan(row){   this.liuZhuanShow=true
      this.liuZhuanForm.studentId=row.studentId
      this.liuZhuanForm.deptId=row.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        console.log(res,"res");
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
      })
    },
    liuZhuan1(row){
      // this.liuZhuanShow=true
      // this.liuZhuanForm.studentId=row.id
      // this.liuZhuanForm.deptId=this.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        console.log(res,"res");
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
        // this.liuZhuanTotal=res.total
      })
    },
    handleSizeChange1(size) {
      this.liuZhuanForm.pageSize = size
      this.liuZhuanForm.pageNumber = 1
      this.liuZhuan1()
    },
    handleCurrentChange1(page) {
      this.liuZhuanForm.pageNumber = page
      this.liuZhuan1()
    }
  },
};
</script>

<style scoped lang="scss">
.el-tabs {
  height: calc(100vh - var(--header-height) - 35px);
  .category {
    float: left;
    text-align: left;
    padding-left: 10px;
  }
}
.el-form {
  padding-bottom: 40px;
  .el-form-item {
    margin-bottom: 5px;
  }
  .stu-search {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 30px;
  }
}
</style>
