<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">
        新增二维码
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="序号" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="ID" align="center" width="220">
        <template slot-scope="scope">
          <span>{{ scope.row.qrcodeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="二维码图片" align="center" min-width="150">
        <template slot-scope="scope">
          <!-- 从截图中显示字段名可能是 qrcodeImage -->
          <el-image 
            v-if="scope.row.qrcodeImage" 
            style="width: 100px; height: 100px"
            :src="getImageUrl(scope.row.qrcodeImage)" 
            :preview-src-list="[getImageUrl(scope.row.qrcodeImage)]">
          </el-image>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="danger" size="mini" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增对话框 -->
    <el-dialog title="新增学校二维码" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 80%; margin: 0 auto;">
        <el-form-item label="二维码图片">
          <el-upload
            action=""
            :http-request="uploadImage"
            :show-file-list="true"
            :before-upload="beforeUpload"
            :limit="10"
            multiple
            list-type="picture-card"
            :file-list="fileList"
            :on-remove="handleRemove"
          >
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="createData">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSchoolQrcodeList, addSchoolQrcode, batchAddSchoolQrcode, deleteSchoolQrcode } from '@/api/schoolQrcode'
import { uploadFile } from '@/utils/upload'
import { imgPrefix, pref } from '@/utils/common'

export default {
  name: 'SchoolQrcode',
  data() {
    return {
      list: [],
      listLoading: true,
      fileList: [],
      uploadedFiles: [],
      temp: {
        schoolId: undefined,
        qrcodeImage: ''
      },
      dialogFormVisible: false,
      rules: {},
      debug: true // 调试模式，用于显示数据
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getImageUrl(url) {
      if (!url) return '';
      if (url.startsWith('http')) {
        return url;
      }
      // 使用与NormalImgEx1组件相同的URL构建逻辑
      return `${process.env.VUE_APP_BASE_API}${pref}${this.$store.getters.deptCode}${url}`;
    },
    getList() {
      this.listLoading = true
      const schoolId = this.$store.getters.userInfo.deptId
      getSchoolQrcodeList({key: schoolId}).then(response => {
        console.log('获取二维码列表响应:', response)
        
        // 修复：直接使用返回的数据
        // API直接返回了数组
        if (Array.isArray(response)) {
          this.list = response
        } else if (response && response.data && Array.isArray(response.data)) {
          this.list = response.data
        } else if (response && response.data) {
          this.list = [response.data]
        } else {
          this.list = []
        }
        
        console.log('处理后的列表数据:', this.list)
        this.listLoading = false
      }).catch((error) => {
        console.error('获取列表数据错误:', error)
        this.listLoading = false
      })
    },
    resetTemp() {
      this.temp = {
        schoolId: this.$store.getters.userInfo.deptId,
        qrcodeImage: ''
      }
      this.fileList = []
      this.uploadedFiles = []
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSchoolQrcode({key: row.id}).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    uploadImage(param) {
      const formData = new FormData()
      formData.append('file', param.file)
      uploadFile(formData).then(response => {
        if (response.code === 0) {
          // 保存上传的文件路径，注意这里不需要包含前缀，因为getImageUrl会添加
          this.uploadedFiles.push(response.data)
          // 添加到fileList用于显示和后续批量上传
          this.fileList.push({
            name: param.file.name,
            url: this.getImageUrl(response.data),
            raw: param.file
          })
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$message.error('上传失败')
        }
      }).catch(() => {
        this.$message.error('上传失败')
      })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      // 从uploadedFiles中移除对应的文件
      const index = this.uploadedFiles.findIndex(item => {
        return file.url.includes(item)
      })
      if (index !== -1) {
        this.uploadedFiles.splice(index, 1)
      }
    },
    createData() {
      if (this.uploadedFiles.length === 0) {
        this.$message.error('请上传二维码图片')
        return
      }
      
      if (this.uploadedFiles.length === 1) {
        // 单个上传
        const tempData = Object.assign({}, this.temp)
        tempData.qrcodeImage = this.uploadedFiles[0]
        
        addSchoolQrcode(tempData).then(() => {
          this.dialogFormVisible = false
          this.$message({
            message: '创建成功',
            type: 'success'
          })
          this.getList()
        })
      } else {
        // 批量上传 - 使用JSON格式传递
        const data = {
          schoolId: this.temp.schoolId,
          imageUrls: this.uploadedFiles
        }
        
        batchAddSchoolQrcode(data).then(() => {
          this.dialogFormVisible = false
          this.$message({
            message: '批量创建成功',
            type: 'success'
          })
          this.getList()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
}
</style>

