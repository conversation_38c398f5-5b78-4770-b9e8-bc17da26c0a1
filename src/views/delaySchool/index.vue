<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.studentName"
                placeholder="姓名或身份证"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.educationReviewStatus"
                placeholder="教育局审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData1"
            ></el-button>
          </el-form-item>
          <el-row>
            <el-form-item>
              <el-button
                  size="small"
                  type="warning"
                  icon="el-icon-download"
                  @click="exportEnrollInfo"
                  v-if="
                  role == 'COUNTY_ADMIN' ||

                  role == 'SCHOOL'
                "
              >导出报名信息
              </el-button
              >
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </div>
    <el-table
        :data="tableData.records"
        border
        stripe
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="50" :fixed="true">
      </el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
          width="220"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
          width="270"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学校审核状态"
          prop="schoolReviewStatus"
          width="200"
          v-if="prefixDeptCode =='130284'"
      ></el-table-column>
      <el-table-column
          align="center"
          label="教育局审核状态"
          prop="educationReviewStatus"
          width="200"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名时间"
          prop="enrollTime"
          width="260"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="360" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="detail(row, $index)"
          >详情
          </el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="getPassButtonDisabled(row)"
              @click="pass(row)"
              v-if="prefixDeptCode === '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR' || role === 'SCHOOL')"
          >通过
          </el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              :disabled="getRejectButtonDisabled(row)"
              @click="reject(row)"
              v-if="prefixDeptCode === '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR' || role === 'SCHOOL')"
          >不通过
          </el-link
          >
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus == '通过'"
              @click="pass(row)"
              v-if="prefixDeptCode !== '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR' || role === 'SCHOOL')"
          >通过
          </el-link
          >
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 10px"
              :disabled="row.educationReviewStatus == '不通过'"
              @click="reject(row)"
              v-if="prefixDeptCode !== '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR' || role === 'SCHOOL')"
          >不通过
          </el-link
          >
          <el-link
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              @click="deleteSign(row)"
              v-if="role == 'COUNTY_ADMIN'"
          >删除报名信息
          </el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <span class="signNum">报名总人数：{{ tableData.signNum }}</span
      >&emsp;
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
      <!-- 报名详情 -->
      <el-dialog
          :visible.sync="modal.stuDetail"
          :close-on-click-modal="false"
          title="学生报名详情"
          center
          width="1240px"
          @close="stuDetailClose"
      >
        <enroll-detail
            :stu-detail="curStuDetail"
            :key="curStuDetail.studentBaseId"
        ></enroll-detail>
        <div>
          <p>审核信息</p>
          <span  v-if="prefixDeptCode =='130284'">学校审核状态：{{ curStuDetail.schoolReviewStatus }} {{ curStuDetail.educationReviewReason }} </span><br>
          <span>教育局审核状态：{{ curStuDetail.educationReviewStatus }}</span>
<!--          <el-alert-->
<!--              style=""-->
<!--              type="success"-->
<!--              effect="dark"-->
<!--              :title="`延缓入学审核状态：${ curStuDetail.educationReviewStatus }`"-->
<!--              :closable="false">-->
<!--          </el-alert>-->
        </div>
        <div class="flex-center" style="margin-top: 20px">
          <el-button
              icon="el-icon-check"
              type="success"
              size="small"
              :disabled="curStuDetail.educationReviewStatus == '通过'"
              @click="pass(curStuDetail)"
              v-if="prefixDeptCode !== '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR')"
          >通过
          </el-button
          >
          <el-button
              icon="el-icon-close"
              type="danger"
              size="small"
              :disabled="curStuDetail.educationReviewStatus == '不通过'"
              @click="reject(curStuDetail)"
              v-if="prefixDeptCode !== '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR')"
          >不通过
          </el-button
          >
          <el-button
              icon="el-icon-check"
              type="success"
              size="small"
              :disabled="curStuDetail.educationReviewStatus != '待审核'"
              @click="pass(curStuDetail)"
              v-if="role == 'SCHOOL'&& prefixDeptCode !=='130284'"
          >通过
          </el-button
          >
          <el-button
              icon="el-icon-close"
              type="danger"
              size="small"
              :disabled="curStuDetail.educationReviewStatus != '待审核'"
              @click="reject(curStuDetail)"
              v-if="role == 'SCHOOL' && prefixDeptCode !=='130284'"
          >不通过
          </el-button
          >
          <el-button
              icon="el-icon-check"
              type="success"
              size="small"
              :disabled="getPassButtonDisabled(curStuDetail)"
              @click="pass(curStuDetail)"
              v-if="prefixDeptCode === '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR')"   >通过
          </el-button
          >
          <el-button
              icon="el-icon-close"
              type="danger"
              size="small"
              :disabled="getRejectButtonDisabled(curStuDetail)"
              @click="reject(curStuDetail)"
              v-if="prefixDeptCode === '130284' && (role === 'COUNTY_ADMIN' || role === 'AUDITOR')"    >不通过
          </el-button
          >
          <el-button type="primary" @click="prevEnrollDetail" size="small">上一条
          </el-button>
          <el-button type="primary" @click="nextEnrollDetail" size="small"
          >下一条
          </el-button
          >
          <el-button size="small" type="info" @click="stuDetailClose"
          >关闭
          </el-button
          >
        </div>
      </el-dialog>

      <!-- 不通过 -->
      <el-dialog
          title="不通过"
          :visible.sync="modal.reject"
          center
          :close-on-click-modal="false"
          width="600px"
          :z-index="9999"
          append-to-body
      >
        <el-form
            :model="rejectForm"
            ref="rejectForm"
            :rules="rejectRules"
            label-position="right"
            label-width="100px"
        >
          <el-form-item prop="reviewStatus" label="驳回类型：">
            <el-select v-model="rejectForm.reviewStatus">
              <el-option label="驳回-修改信息" :value="3"></el-option>
<!--              <el-option label="驳回-不可再报" :value="4"></el-option>-->
            </el-select>
          </el-form-item>
          <el-form-item prop="reviewReason" label="驳回原因：">
            <el-input
                type="textarea"
                :rows="5"
                v-model.trim="rejectForm.reviewReason"
                placeholder="请输入驳回原因，最多200个字符"
                style="width: 400px"
                maxlength="200"
                show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="tips">
          <div>注释：</div>
          <div>
            1、驳回-修改信息：选择修改信息，可以修改基础信息
          </div>
<!--          <div>-->
<!--            2、驳回-不可再报：选择不可再报，重新填写报名信息，不能选择原报名学校-->
<!--          </div>-->
        </div>
        <div class="flex-center sd-m-t-30">
          <el-button size="small" @click="switchModal('reject', false)"
          >取消
          </el-button
          >
          <el-button size="small" type="primary" @click="confirmReject"
          >确定
          </el-button
          >
        </div>
      </el-dialog>

      <!-- 删除报名信息 -->
      <el-dialog
          title="删除报名信息"
          :visible.sync="modal.deleteSign"
          center
          :close-on-click-modal="false"
          width="600px"
      >
        <el-form
            :model="deleteSignForm"
            ref="deleteSignForm"
            :rules="deleteSignRules"
            label-position="right"
            label-width="130px"
        >
          <el-form-item prop="content" label="删除报名原因：">
            <el-input
                type="textarea"
                :rows="5"
                v-model.trim="deleteSignForm.content"
                placeholder="请输入删除报名原因，最多200个字符"
                maxlength="200"
                show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="flex-center sd-m-t-30">
          <el-button size="small" @click="switchModal('deleteSign', false)"
          >取消
          </el-button
          >
          <el-button size="small" type="primary" @click="confirmDeleteSign"
          >确定
          </el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  passAudit,
  rejectAudit,
  deleteEnroll, getDelayEnrollList , getAuditRecord,
} from "@/api/enrollment";
import {pref, tangShanPriorityTypeList} from "@/utils/common";
import EnrollDetail from "@/components/EnrollDetailYanXue.vue";

export default {
  mixins: [TableMixin, ModalMixin],
  components: {EnrollDetail},
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      search: {
        studentName: "",
        educationReviewStatus: "",
        enrollStage: "0",
        schoolType: "1",
        delayType: 1,
      },
      typeList: [],
      modal: {
        reject: false,
        adjust: false,
        report: false,
        notice: false,
        priority: false,
        stuDetail: false,
        revokePublic: false,
        deleteSign: false,
        enrollRecord: false
      },
      schoolList: [],
      multipleSelection: [],
      // 驳回
      rejectForm: {
        id: 0,
        reviewReason: "",
        reviewStatus: "",
      },
      rejectRules: {
        reviewStatus: [
          {
            required: true,
            message: "请选择驳回类型",
            trigger: "change",
          },
        ],
        reviewReason: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      adjustInfo: {
        enRollId: "",
        studentName: "",
        enrollSchoolName: "",
        adjustSchoolName: "",
      },
      priorityInfo: {
        studentName: "",
        studentIdCardNumber: "",
      },
      priorityTypeList: tangShanPriorityTypeList,
      // 撤销公示结果
      revokePublicForm: {
        id: "",
        content: "",
      },
      revokePublicRules: {
        content: [
          {
            required: true,
            message: "请输入撤销公示原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 删除报名信息
      deleteSignForm: {
        id: "",
        content: "",
      },
      deleteSignRules: {
        content: [
          {
            required: true,
            message: "请输入删除报名原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      auditRecordList: [],
      loadingAuditRecord: true,
      // 当前学生报名详情
      curStuDetail: {},
      // 下一条报名详情index
      index: 0,
      // 功能开关状态
      funcStatusInfo: {
        deleteStatus: false,
        publicStatus: false,
        noticeStatus: false,
      },
      // 审核情况中指定的报名详情
      curStuRecordId: ''
    };
  },
  computed: {
    schoolListAvailable() {
      return this.schoolList.filter(
          (item) =>
              item.deptName !=
              (this.adjustInfo.adjustSchoolName
                  ? this.adjustInfo.adjustSchoolName
                  : this.adjustInfo.enrollSchoolName)
      );
    },
  },
  async created() {
  },
  methods: {
    // 获取通过按钮的禁用状态
    getPassButtonDisabled(row) {
      if (this.role === 'SCHOOL') {
        // 学校管理：只要学校审核状态不为待审核，都不可以点击
        return row.schoolReviewStatus !== '待审核';
      } else if (this.role === 'COUNTY_ADMIN' || this.role === 'AUDITOR') {
        // 教育局或教育局审核：只要状态不是通过状态都可以点击
        return row.educationReviewStatus === '通过';
      }
      return false;
    },

    // 获取不通过按钮的禁用状态
    getRejectButtonDisabled(row) {

      if (this.role === 'SCHOOL') {
        // 学校管理：只要学校审核状态不为待审核，都不可以点击
        return row.schoolReviewStatus !== '待审核';
      } else if (this.role === 'COUNTY_ADMIN' || this.role === 'AUDITOR') {
        // 教育局或教育局审核：只要状态不是通过状态都可以点击
        return row.educationReviewStatus === '通过';
      }
      return false;
    },

    // 列表
    getTableData1() {
      this.search.pageNumber = 1;
      this.getTableData();
    },
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
    // 详情 - 下一条
    nextEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
          this.search.pageNumber * this.search.pageSize > this.total ||
          this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.$message.error("已是最后一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.search.pageNumber += 1;
          getDelayEnrollList(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.curStuDetail = this.tableData.records[0];
            this.index = 0;
            this.getAuditRecord();
          });
        }
      }
    },
    // 查询审核情况
    getAuditRecord() {
      this.auditRecordList = [];
      this.loadingAuditRecord = true;
      getAuditRecord(
          {key: this.curStuDetail.studentBaseId},
          this.prefixDeptCode
      )
          .then((res) => {
            this.auditRecordList = res;
          })
          .finally(() => {
            this.loadingAuditRecord = false;
          });
    },
    // 详情 - 上一条
    prevEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
          this.search.pageNumber * this.search.pageSize > this.total ||
          this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index > 0) {
          this.index -= 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.$message.error("已是第一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index > 0) {
          this.index -= 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.search.pageNumber -= 1;
          getDelayEnrollList(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.index = this.tableData.records.length - 1;
            this.curStuDetail = this.tableData.records[this.index];
            this.getAuditRecord();
          });
        }
      }
    },
    getTableData() {
      this.tableLoading = true;
      getDelayEnrollList(this.search, this.prefixDeptCode)
          .then((res) => {
            this.tableData = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 通过
    pass(row) {
      this.$confirm("确认该学生信息无误，审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        passAudit(
            {id: row.studentBaseId, enrollStage: "2"},
            this.prefixDeptCode
        ).then(() => {
          this.$message.success("操作成功");
          this.getTableData1();
        });
      });
    },
    // 驳回
    reject(row) {
      this.switchModal("reject", true);
      this.$nextTick(() => {
        this.$refs.rejectForm.resetFields();
        this.rejectForm.id = row.studentBaseId;
      });
    },
    // 驳回确定
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          let params = {};
          if (this.role == "COUNTY_ADMIN" || this.role == "AUDITOR") {
            params.id = this.rejectForm.id;
            params.educationReviewStatus = this.rejectForm.reviewStatus;
            params.educationReviewReason = this.rejectForm.reviewReason;
          } else if (this.role == "SCHOOL") {
            params.id = this.rejectForm.id;
            params.schoolReviewStatus = this.rejectForm.reviewStatus;
            params.schoolReviewReason = this.rejectForm.reviewReason;
          }
          rejectAudit(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("reject", false);
            this.getTableData();
          });
        }
      });
    },
    exportEnrollInfo() {
      if (this.prefixDeptCode != "130423") {
        this.$download(
            `${pref}${this.prefixDeptCode}/biz/delayQuery/exportDelaySchoolStudent`,
            this.search,
            "xlsx",
            "延缓入学信息.xlsx"
        ).then((res) => {
          this.$message.success("导出成功");
        });
      } else {
        // 临漳130423
        this.$download(
            `${pref}${this.prefixDeptCode}/biz/recruitStudent/lzExportStudent`,
            this.search,
            "xlsx",
            "小学导出报名信息.xlsx"
        ).then((res) => {
          this.$message.success("导出成功");
        });
      }
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.index = index;
      this.modal.stuDetail = true;
      // this.getAuditRecord();
      // this.getAuditStatus();
    },
    // 删除报名信息
    deleteSign(row) {
      this.switchModal("deleteSign", true);
      this.$nextTick(() => {
        this.$refs.deleteSignForm.resetFields();
        this.deleteSignForm.key = row.studentBaseId;
      });
    },
    // 删除报名信息确认
    confirmDeleteSign() {
      this.$refs.deleteSignForm.validate((valid) => {
        if (valid) {
          let params = this.deleteSignForm;
          deleteEnroll(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("deleteSign", false);
            this.getTableData();
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}

.notice-num {
  font-size: 16px;
}

.signNum {
  color: #606266;
  font-size: 13px;
  height: 28px;
  line-height: 28px;
}
</style>
