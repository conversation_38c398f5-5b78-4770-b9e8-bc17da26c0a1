<template>
  <div>
    <el-tabs
      tab-position="left"
      :stretch="true"
      type="border-card"
      v-model="deptCode"
      @tab-click="tabClick"
    >
      <el-tab-pane
        v-for="item in overviewAnddeptOptions"
        :label="item.deptName"
        :name="item.deptCode"
        :key="item.id"
      >
        <div class="sd-option-container">
          <div class="sd-options">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              @click="importBatch"
              size="small"
              v-if="deptCode != '0'"
              >批量清除</el-button
            >
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              @click="handleClear"
              size="small"
              >清除</el-button
            >
          </div>
          <div class="sd-search">
            <el-select
              size="small"
              v-model="search.deptId"
              placeholder="所属区县"
              clearable
              v-if="deptCode == '0'"
            >
              <el-option
                v-for="item in deptOptions"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
            <el-input
              v-model.trim="search.keywords"
              placeholder="请输入学生身份证号"
              size="small"
              clearable
              style="width: 260px"
            ></el-input>
            <el-button
              type="primary"
              class="sd-btn-search"
              icon="el-icon-search"
              @click="getList"
              size="small"
            />
          </div>
        </div>
        <el-table
          :data="tableData.records"
          border
          stripe
          v-loading="tableLoading"
        >
          <el-table-column
            prop="deptName"
            label="所属区县"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="schoolName"
            label="所属学校"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="studentName"
            label="学生姓名"
            align="center"
          ></el-table-column>
          <el-table-column prop="twinsFlag" label="是否多胞胎" align="center">
            <template slot-scope="scope">
              {{ scope.row.twinsFlag == 1 ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="idCard"
            label="身份证号"
            align="center"
          ></el-table-column>
          <el-table-column prop="createTime" label="清除时间" align="center">
          </el-table-column>
          <el-table-column prop="creatorName" label="操作账号" align="center">
          </el-table-column>
          <el-table-column
            label="操作"
            width="200"
            fixed="right"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-link
                type="success"
                @click="recover(row)"
                :underline="false"
                icon="el-icon-s-tools"
                v-if="row.recoverFlag == '2'"
                >恢复</el-link
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="page-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="search.pageNumber"
            layout="total, prev, pager, next, sizes"
            :page-sizes="$pageSizes"
            :total="total"
          >
          </el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加、修改 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <div class="stu-search">
          <el-form-item lable="" prop="idNumber">
            <el-input
              v-model.trim="form.idNumber"
              placeholder="请输入学生身份证号"
              size="small"
              clearable
              style="width: 366px"
            ></el-input>
          </el-form-item>
          <el-button
            type="primary"
            class="sd-btn-search"
            icon="el-icon-search"
            @click="handleSearch"
            size="small"
          />
        </div>
        <el-form-item
          label="学生姓名："
          label-position="right"
          label-width="150px"
        >
          <span>{{ form.studentName }}</span>
        </el-form-item>
        <el-form-item
          label="是否多胞胎："
          label-position="right"
          label-width="150px"
        >
          <span>{{ switchTwinsFlag }}</span>
        </el-form-item>
        <el-form-item
          label="被绑定双胞胎："
          label-position="right"
          label-width="150px"
          v-if="form.twinsFlag"
        >
          <ul v-for="(item, index) in form.twins" :key="index">
            <li>{{ item.studentName }}&emsp;{{ item.idCard }}</li>
          </ul>
        </el-form-item>
        <el-form-item
          label="所属区县："
          label-position="right"
          label-width="150px"
        >
          <span>{{ form.deptName }}</span>
        </el-form-item>
        <el-form-item
          label="所属学校："
          label-position="right"
          label-width="150px"
        >
          <span>{{ form.schoolName }}</span>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button
          type="default"
          @click="switchModal('addOrEdit', false)"
          size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="handleSubmit"
          size="small"
          :loading="submitLoading"
          :disabled="!form.studentId"
          >清除数据</el-button
        >
      </div>
    </el-dialog>

    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :loading="submitBatchLoading"
          :disabled="fileList.length == 0"
          >清除数据</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getClearStudentList,
  deleteStudentPageList,
  getStudentInfo,
  clearStuBatch,
  privateDeleteEnrollInfo,
  privateRecoverEnrollInfo,
  privateClearStuBatch, getPrivateStudentInfo
} from "@/api/sysConfig";
import { getDepts } from "@/api/common";
import { pref } from "@/utils/common";
export default {
  name: "clearSignData",
  mixins: [TableMixin, ModalMixin],
  components: {},
  data() {
    return {
      prefixDeptCode: "0", // 当前选项卡区县
      search: {
        deptId: "",
        keywords: "",
        schoolType:2
      },
      deptCode: "0",
      modal: {
        addOrEdit: false,
        import: false,
      },
      tableLoading: false,
      dialogTitle: "",
      form: {
        idNumber: "",
        idCard: "",
        studentId: "",
        studentName: "",
        deptCode: "",
        deptName: "",
        schoolName: "",
        twinsFlag: "",
        twins: null,
      },
      deptOptions: [],
      overviewAnddeptOptions: [],
      rules: {
        idNumber: [
          { required: true, message: "请输入学生身份证号", trigger: "change" },
        ],
      },
      submitLoading: false,
      submitBatchLoading: false,
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
    };
  },
  created() {},
  computed: {
    switchTwinsFlag() {
      return this.form.twinsFlag === ""
        ? ""
        : this.form.twinsFlag === true
        ? "是"
        : "否";
    },
  },
  methods: {
    // 初次查询
    async getTableData() {
      if (this.prefixDeptCode != "0") {
        this.getList();
      } else {
        let depts = await getDepts({ level: 2 });
        this.deptOptions = depts.filter((item) => item.openStatus == 1);
        this.overviewAnddeptOptions = this.deptOptions.slice();
        let overview = { id: null, deptName: "总览删除" };
        this.overviewAnddeptOptions.unshift(overview);
        this.getList();
      }
    },
    // 查询清除报名列表
    getList() {
      this.tableData.records = [];
      this.tableLoading = true;
      deleteStudentPageList(this.search)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // getList() {
    //   this.tableData.records = [];
    //   this.tableLoading = true;
    //   getClearStudentList(this.search, this.prefixDeptCode)
    //     .then((res) => {
    //       this.tableData = res;
    //     })
    //     .finally(() => {
    //       this.tableLoading = false;
    //     });
    // },
    // 点击tab
    tabClick() {
      this.prefixDeptCode = this.deptCode;
      this.tableData.records = [];
      this.tableLoading = true;
      let params = JSON.parse(JSON.stringify(this.search));
      if (this.deptCode == "0") {
        params.deptId = "";
      } else {
        params.deptId = this.deptOptions.filter(
          (item) => item.deptCode == this.deptCode
        )[0].id;
      }
      this.search.deptId = params.deptId;
      deleteStudentPageList(params)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 清除
    handleClear() {
      this.switchModal("addOrEdit", true);
      this.dialogTitle = "清除学生报名数据";
      this.$nextTick(() => {
        this.$refs["form"].resetFields();
        this.form.idNumber = "";
        this.form.idCard = "";
        this.form.studentId = "";
        this.form.studentName = "";
        this.form.deptCode = "";
        this.form.deptName = "";
        this.form.schoolName = "";
        this.form.twinsFlag = "";
        this.form.twins = "";
      });
    },
    // 查询学生
    handleSearch() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          getPrivateStudentInfo({ key: this.form.idNumber, deptId: this.search.deptId }).then((res) => {
            if (res) {
              this.form.idCard = res.idCard;
              this.form.studentId = res.studentId;
              this.form.studentName = res.studentName;
              this.form.deptCode = res.deptCode;
              this.form.deptName = res.deptName;
              this.form.schoolName = res.schoolName;
              this.form.twinsFlag = res.twinsFlag;
              this.form.twins = res.twins;
            } else {
              this.form.idCard = "";
              this.form.studentId = "";
              this.form.studentName = "";
              this.form.deptCode = "";
              this.form.deptName = "";
              this.form.schoolName = "";
              this.form.twinsFlag = "";
              this.form.twins = null;
              this.$message.warning("未查询到学生数据");
            }
          });
        }
      });
    },
    // handleSearch() {
    //   this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       getEnrollStudentInfo(
    //         { key: this.form.idNumber },
    //         this.prefixDeptCode
    //       ).then((res) => {
    //         if (res) {
    //           this.form.studentId = res.id;
    //           this.form.studentName = res.studentName;
    //           this.form.deptName = res.deptName;
    //           this.form.enrollSchoolName = res.enrollSchoolName;
    //         } else {
    //           this.form.studentId = "";
    //           this.form.studentName = "";
    //           this.form.deptName = "";
    //           this.form.enrollSchoolName = "";
    //           this.$message.warning("未查询到学生数据");
    //         }
    //       });
    //     }
    //   });
    // },
    // 清除提交
    handleSubmit() {
      console.log(this.prefixDeptCode);
      this.submitLoading = true;
      this.$confirm(
        `确定清除 " ${this.form.studentName} " 的报名信息吗？`,
        "提示",
        {
          type: "warning",
          closeOnClickModal: false,
        }
      )
        .then(() => {
          privateDeleteEnrollInfo({ key: this.form.studentId }, this.form.deptCode)
            .then((res) => {
              this.$message.success("清除成功");
              this.modal.addOrEdit = false;
              this.tabClick();
            })
            .finally(() => {
              this.submitLoading = false;
            });
        })
        .catch(() => {
          this.submitLoading = false;
        });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "批量清除学生报名数据";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        `${pref + this.prefixDeptCode}/privatee/biz/enrollment/exportBatchClearStuExcel`,
        {},
        "xls",
        "批量清除学生报名数据模版.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      this.submitBatchLoading = true;
      this.$confirm(`确定清除导入的学生报名信息吗？`, "提示", {
        type: "warning",
        closeOnClickModal: false,
      })
        .then(() => {
          let formData = new FormData();
          formData.append("file", this.fileList[0]);
          privateClearStuBatch(formData, this.prefixDeptCode)
            .then((res) => {
              if (res.length > 0) {
                this.errorMessages = res;
              } else {
                this.$message.success("清除成功");
                this.submitBatchLoading = false;
                this.switchModal("import", false);
                this.getList();
              }
            })
            .finally(() => {
              this.submitBatchLoading = false;
            });
        })
        .catch(() => {
          this.submitBatchLoading = false;
        });
    },
    // 恢复
    recover(row) {
      this.$confirm(`确定恢复 " ${row.studentName} " 的报名信息吗？`, "提示", {
        type: "warning",
        closeOnClickModal: false,
      }).then(() => {
        privateRecoverEnrollInfo({ 
          id: row.id,
          userId: row.studentId,
          studentIdCardNumber: row.idCard
        }, row.deptCode).then((res) => {
          this.$message.success("恢复成功");
          this.getList();
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-tabs {
  height: calc(100vh - var(--header-height) - 35px);
  .category {
    float: left;
    text-align: left;
    padding-left: 10px;
  }
}
.el-form {
  padding-bottom: 40px;
  .el-form-item {
    margin-bottom: 5px;
  }
  .stu-search {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-bottom: 30px;
  }
}
</style>
