<template>
  <div class="section ad-form">
    <el-skeleton
        :loading="loadingAdForm"
        animated
    >
      <template slot="default">
        <el-alert
            style="margin-bottom: 10px;"
            type="success"
            effect="dark"
            :title="`当前报名学校：${ adSchool.deptName }`"
            :closable="false">
        </el-alert>
        <dl class="form-group">
          <dt class="f-group-title">报名类别</dt>
          <dd class="f-group-detail">
            <el-button size="small" type="primary" @click="openTpSelect">修改报名类别</el-button>
          </dd>
        </dl>
        <el-form ref='form' :model="form" :rules="rules" size="small" :inline="true" label-width="170px">
          <!-- 普通字段 -->
          <dl v-for="item, idx in normalForm" :key="item.typeConfigId" class="form-group">
            <dt class="f-group-title">{{ item.infoName }}</dt>
            <dd class="f-group-detail">
              <div>
                <template v-for="fi, fidx in item._normalItem">
                  <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item" v-show="shouldShowSiblingField(fi.fieldId) && shouldShowResidenceField(fi.fieldId)">
                    <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                    <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                  </el-form-item>
                </template>
              </div>
              <template v-for="fi, fidx in item._imgItem">

                <el-form-item v-if="fi.fieldEnglish!=='divorceAgreementInfo' && fi.fieldEnglish!=='divorceFosterInfo'  && fi.fieldEnglish!=='divorceSign' " :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                </el-form-item>
                <el-form-item v-else-if="fi.fieldEnglish==='divorceAgreementInfo'&& isGuardianDivorced" :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                </el-form-item>
                <el-form-item v-else-if="fi.fieldEnglish==='divorceFosterInfo' && isGuardianDivorced" :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                </el-form-item>
                <el-form-item v-else-if="fi.fieldEnglish==='divorceSign' && isGuardianDivorced" :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                  <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                </el-form-item>
              </template>
              <div v-if="entryId == 74 || entryId == 75 || tpAndSchoolForm.entry3 == 76 || entryId == 77 || entryId == 80 || entryId == 82 || entryId == 83 || entryId == 84 || entryId == 85 || entryId == 88" style="color: #f56c6c; font-size: 14px; margin-top: 10px;">
                注：学生与监护人户口不在一起的需要提供医学出生证明。
              </div>
              <div class="f-group-tips">
                <div class="tips-item" v-if="item.typeConfigId == 2">
                  <p>注：</p>
                  <ol>
                    <li>含*项均为必填项，请核查后仔细填写，确保无误</li>
                    <li>上传的照片大小应小于6M</li>
                  </ol>
                </div>
              </div>
            </dd>
          </dl>
          <!-- 双胞胎 -->
          <dl class="form-group" v-if="siblings.allList.length > 0">
            <dt class="f-group-title">
              <el-checkbox v-model="siblings.isHaveSib" @change="initOrHideSib">双胞胎信息</el-checkbox>
            </dt>
            <dd class="f-group-detail" v-show="siblings.isHaveSib">
              <ul class="sib-list">
                <li class="sib-item" v-for="si, sIdx in siblings.list" :key="si.typeConfigId">
                  <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
                  <div>
                    <template v-for="fi, fidx in si._normalItem">
                      <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item" v-show="shouldShowSiblingField(fi.fieldId) && shouldShowResidenceField(fi.fieldId)">
                        <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                        <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                      </el-form-item>
                    </template>
                  </div>
                  <div class="f-g-d-img-list">
                    <template v-for="fi, fidx in si._imgItem">
                      <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                        <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                      </el-form-item>
                    </template>
                  </div>
                </li>
              </ul>
              <div class="sib-actions">
                <el-button size="small" type="primary" @click="sibAdd" v-if="siblings.list.length == 1">添加</el-button>
                <el-button size="small" type="danger" @click="sibDel" v-if="siblings.list.length == 2">删除</el-button>
              </div>
            </dd>
          </dl>
          <!-- 随迁 -->
          <dl class="form-group" v-if="sq.isSQ">
            <dt class="f-group-title both-side">
              <span>{{ sq.tpCn }}</span>
              <el-radio-group v-model="sq.tpCn" @input="sqTpChange" size="mini">
                <el-radio-button :key="sqRadioItem.typeConfigId" v-for="sqRadioItem in sq.allList" :label="sqRadioItem.infoName"></el-radio-button>
              </el-radio-group>
            </dt>
            <dd class="f-group-detail">
              <div v-for="sqItem in sq.list" :key="sqItem.typeConfigId">
                <div>
                  <template v-for="fi, fidx in sqItem._normalItem">
                    <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item" v-show="shouldShowSiblingField(fi.fieldId) && shouldShowResidenceField(fi.fieldId)">
                      <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                      <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                    </el-form-item>
                  </template>
                </div>
                <div class="f-g-d-img-list">
                  <template v-for="fi, fidx in sqItem._imgItem">
                    <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                      <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                    </el-form-item>
                  </template>
                </div>
              </div>
            </dd>
          </dl>
          <!-- 房产 -->
          <dl class="form-group" v-if="propertyForm.list.length > 0">
            <dt class="f-group-title both-side">
              <span>房产信息</span>
              <el-select style="width: 200px" size="small" v-model="propertyForm.tp" @change="ppTabChange">
                <el-option v-for="ppItem in propertyForm.list" :value="ppItem.typeConfigId" :label="ppItem.infoName" :key="ppItem.typeConfigId"></el-option>
              </el-select>
            </dt>
            <dd class="f-group-detail">
              <div v-for="ppItem in propertyForm.curList" :key="ppItem.typeConfigId">
                <div>
                  <template v-for="fi, fidx in ppItem._normalItem">
                    <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item" v-show="shouldShowSiblingField(fi.fieldId) && shouldShowResidenceField(fi.fieldId)">
                      <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                      <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                    </el-form-item>
                  </template>
                </div>
                <div class="f-g-d-img-list">
                  <template v-for="fi, fidx in ppItem._imgItem">
                    <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                      <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                    </el-form-item>
                  </template>
                </div>
              </div>
            </dd>
          </dl>

          <!--优抚对象-->
          <dl class="form-group" v-if="youFuForm.list.length > 0">
            <dt class="f-group-title both-side">
              <span>优抚信息</span>
              <el-select style="width: 200px" size="small" v-model="youFuForm.tp" @change="ufTabChange">
                <el-option v-for="ppItem in youFuForm.list" :value="ppItem.typeConfigId" :label="ppItem.infoName" :key="ppItem.typeConfigId"></el-option>
              </el-select>
            </dt>
            <dd class="f-group-detail">
              <div v-for="ppItem in youFuForm.curList" :key="ppItem.typeConfigId">
                <div>
                  <template v-for="fi, fidx in ppItem._normalItem">
                    <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item" v-show="shouldShowSiblingField(fi.fieldId) && shouldShowResidenceField(fi.fieldId)">
                      <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                      <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6|| fi.inputItemCode == 16"></normal-select>
                    </el-form-item>
                  </template>
                </div>
                <div class="f-g-d-img-list">
                  <template v-for="fi, fidx in ppItem._imgItem">
                    <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                      <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                    </el-form-item>
                  </template>
                </div>
              </div>
            </dd>
          </dl>

          <template v-if="others.list.length > 0">
            <dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
              <dt class="f-group-title" v-if="prefixDeptCode == '130481'">乡镇购房材料</dt><!-- 武安 -->
              <dt class="f-group-title" v-else>其他材料证明</dt>
              <dd class="f-group-detail">
                <div>
                  <template v-for="(fi, fidx) in oItem._normalItem">
                    <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item" v-show="shouldShowSiblingField(fi.fieldId) && shouldShowResidenceField(fi.fieldId)">
                      <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                      <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange" v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                    </el-form-item>
                  </template>
                </div>
                <template v-for="fi, fidx in oItem._imgItem">
                  <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                    <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi" @value-change="uploadValueChange"></normal-img-upload>
                  </el-form-item>
                </template>
              </dd>
            </dl>
          </template>

        </el-form>
        <div style="display: flex; justify-content: center; align-items: center;">
          <el-button @click="_goBack()">返回</el-button>
          <el-button type="primary" @click="submitAdForm">提交</el-button>
        </div>
      </template>
    </el-skeleton>
    <el-dialog :visible.sync="modal.tpAndSchoolSelect" title="报名类别与学校选择" width="500px" center :close-on-click-modal="false" :show-close="false">
      <el-form ref="tpAndSchoolForm" :model="tpAndSchoolForm" :rules="tpFormRules" label-width="120px">
        <el-form-item prop="entry1" label="报名入口：" v-if="curRole == 'COUNTY_ADMIN'">
          <span v-if="$route.query.transferType === '区外转学报名添加'">区外转学</span>
          <el-select size="small" placeholder="请选择报名入口" v-model="tpAndSchoolForm.entry1" @change="isInTime1" v-show="$route.query.transferType !== '区外转学报名添加'">
            <el-option v-for="en1 in entry1List" :key="en1.id" :label="en1.name" :value="en1.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="entry2" label="报名学段：" v-if="curRole == 'COUNTY_ADMIN'">
          <span v-if="$route.query.transferType === '区外转学报名添加'">初中</span>
          <span v-else-if="tpAndSchoolForm.entry2 == 7 || tpAndSchoolForm.entry2 == 63">小学</span>
          <span v-else-if="tpAndSchoolForm.entry2 == 8 || tpAndSchoolForm.entry2 == 64">初中</span>
          <span v-else-if="tpAndSchoolForm.entry2 == 123 || tpAndSchoolForm.entry2 == 129">小学</span>
          <span v-else-if="tpAndSchoolForm.entry2 == 124 || tpAndSchoolForm.entry2 == 130">初中</span>
          <span v-else>请选择报名入口</span>
        </el-form-item>
        <el-form-item prop="entry3" label="报名类别：">
          <el-select size="small" placeholder="请选择报名类别" v-model="tpAndSchoolForm.entry3" v-loading="entry3Load" element-loading-spinner="el-icon-loading">
            <el-option v-for="en3 in entry3List" :key="en3.setupId" :label="en3.idsName" :value="en3.setupId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="school" label="报名学校：" v-if="curRole == 'COUNTY_ADMIN'">
          <el-select filterable size="small" placeholder="请选择报名学校" v-model="tpAndSchoolForm.school" v-loading="adSchoolLoad" element-loading-spinner="el-icon-loading">
            <el-option v-for="sc in adSchoolList" :key="sc.id" :label="sc.deptName" :value="sc.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="small" @click="cancelModal">取消</el-button>
        <el-button size="small" type="primary" @click="confirmAndQryForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSetupSaveIds,
  getSetupSaveDetail,
  getEntryBySchool,
  qryAdFormByEntry,
  addAd
} from "@/api/enrollment.js"
import { schoolList } from "@/api/setting.js"
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import { LoopFn } from "@/mixins/loopFn"
import ModalMixin from "@/mixins/ModalMixin"
export default {
  components: {
    NormalInput,
    NormalSelect,
    NormalImgUpload,
  },
  mixins: [ LoopFn, ModalMixin ],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      curRole: this.$store.state.user.role,
      // 报名类别1列表
      entry1List: [],
      // 报名类别2列表
      entry2List: [],
      entry2Load: false,
      // 报名类别3列表
      entry3List: [],
      entry3Load: false,
      // 要报名的学校列表
      adSchoolList: [],
      adSchoolLoad: false,
      isGuardianDivorced: LoopFn.data().isGuardianDivorced, // 监护人是否离异
      isFollowSibling: '0', // 是否长幼随学，'0'表示否，'1'表示是
      residenceType: '0', // 居住证类型：'0'=居住证，'1'=居住凭证
      // 类别和学校弹窗form
      tpAndSchoolForm: {
        entry1: '',
        entry2: '',
        entry3: '',
        school: ''
      },
      tpFormRules: {
        entry3: [{ required: true, message: '请选择报名类别', trigger: 'change' }],
        school: [{ required: true, message: '请选择报名学校', trigger: 'change' }]
      },
      // select表单域用到的额外参数
      schoolParams4Select: {
        deptCode: '',
        nature: ''
      },
      // 已选的报名学校
      adSchool: {
        deptName: '',
        id: ''
      },
      modal: {
        tpAndSchoolSelect: true
      },
      isDisabled: false, // 是否为残疾儿童
      basicInfo:"",
      schoolType:0
    }
  },
  async created() {
    // 如果学校，报名学校即当前学校
    if (this.curRole == 'SCHOOL') {
      let storage = this.$store.state.user
      this.adSchool.id = storage.userInfo.deptId
      this.adSchool.deptName = storage.userInfo.deptName
      this.getEntryTp1BySchool(storage.userInfo.deptId)
    } else if (this.curRole == 'COUNTY_ADMIN') {
      // 区教育局
      this.getEntryTp1()
    }

    // 处理区外转学参数
    if (this.$route.query.transferType === "区外转学报名添加") {
      console.log(this.$route.query.transferType); // 打印传入的参数

      // 设置固定的报名入口和学段
      // 区外转学的entry1值为232
      this.tpAndSchoolForm.entry1 = '232';

      // 设置学段为初中(8)
      this.tpAndSchoolForm.entry2 = '8';

      // 延迟一下，确保entry1List已加载
      setTimeout(() => {
        // 直接使用232获取报名类别，而不是通过isInTime1方法
        this.getEntryTp3('236');
        // 获取学校列表
        if (this.curRole == 'COUNTY_ADMIN') {
          this.getSchoolList();
        }
      }, 300);
    }
  },
  watch:{
    isGuardianDivorced(newVal) {
      if (!newVal) {
        this.clearDivorceFields();
      }
    },
    isDisabled(newVal) {
      // 监听残疾儿童状态变化时更新界面
      this.updateDisabilityCertificateVisibility(newVal);
    }
  },
  methods: {
    // 入口 - 学校管理员
    getEntryTp1BySchool(id) {
      this.tpAndSchoolForm.entry3 = ''
      this.entry3Load = true
      getEntryBySchool({
        key: id
      }, this.prefixDeptCode).then(res => {
        this.entry3List = res
        this.entry3Load = false
      }).catch(() => {
        this.entry3Load = false
      })
    },
    clearDivorceFields() {
      // 找到监护人信息模块（typeConfigId: "2"）
      const guardianInfo = this.originList.find(item => item.typeConfigId === "2");
      if (!guardianInfo) return;

      // 定义需要清空的离婚相关字段
      const divorceFields = ['divorceAgreementInfo', 'divorceFosterInfo', 'divorceSign'];

      // 遍历 _imgItem 并清空相关字段
      guardianInfo._imgItem?.forEach(item => {
        if (divorceFields.includes(item.fieldEnglish)) {
          item.fieldValue = null; // 或者 item.fieldValue = [] 如果是数组类型
        }
      });
    },
    // 入口
    getEntryTp1() {
      getSetupSaveIds({ key: 1 }, this.prefixDeptCode).then(res => {
        this.entry1List = res
      })
    },
    // 学段
    getEntryTp2(k) {
      let tp = this.$route.query.period
      if (k === '2') {
        if (tp === 'primary') {
          this.tpAndSchoolForm.entry2 = '7'
        } else if (tp === 'junior') {
          this.tpAndSchoolForm.entry2 = '8'
        }
      } else if (k === '3') {
        if (tp === 'primary') {
          this.tpAndSchoolForm.entry2 = '63'
        } else if (tp === 'junior') {
          this.tpAndSchoolForm.entry2 = '64'
        }
      } else if (k === '116') {
        if (tp === 'primary') {
          this.tpAndSchoolForm.entry2 = '123'
        } else if (tp === 'junior') {
          this.tpAndSchoolForm.entry2 = '124'
        }
      } else if (k === '117') {
        if (tp === 'primary') {
          this.tpAndSchoolForm.entry2 = '129'
        } else if (tp === 'junior') {
          this.tpAndSchoolForm.entry2 = '130'
        }
      }
      this.isInTime1(this.tpAndSchoolForm.entry2)
      // this.getEntryTp3(this.tpAndSchoolForm.entry2)
      // this.entry2Load = false
      /* getSetupSaveIds({ key: k }, this.prefixDeptCode).then((res) => {
        this.entry2List = res
        this.entry2Load = false
      }).catch(err => {
        this.entry2Load = false
      }) */
    },
    // 类别
    getEntryTp3(k) {
      this.tpAndSchoolForm.entry3 = ''
      this.entry3Load = true
      getSetupSaveDetail({ key: k }, this.prefixDeptCode).then((res) => {
        this.entry3List = res
        this.entry3Load = false
      }).catch(() => {
        this.entry3Load = false
      })
    },
    // 隐藏残疾证明上传组件
    hideDisabilityCertificateUpload() {
      this.$nextTick(() => {
        // 初始状态下隐藏残疾证明上传组件
        this.updateDisabilityCertificateVisibility(this.isDisabled);
      });
    },
    // 更新残疾证明上传组件的可见性
    updateDisabilityCertificateVisibility(isVisible) {
      const disabilityCertItem = document.querySelector('.el-form.el-form--inline .f-group-detail .img-item:nth-child(2)');
      if (disabilityCertItem) {
        disabilityCertItem.style.display = isVisible ? '' : 'none';
      }
    },
    // 获取学校
    getSchoolList() {
      this.tpAndSchoolForm.school = ''
      this.adSchoolLoad = true
      let params = {
        keywords: "",
        nature: this.tpAndSchoolForm.entry1 - 1,
        period: "",
        deptCode: this.prefixDeptCode,
        type: 1,
        rejectSchoolId: '',
        pageNumber: 1,
        pageSize: 9999
      }
      let entry2 = this.tpAndSchoolForm.entry2
      // 城区幼儿园
      if (entry2 == 62) {
        params.period = 1
      } else if (entry2 == 7 || entry2 == 63) {
        // 7乡镇小学，63城区小学
        params.period = 2
      } else if (entry2 == 8 || entry2 == 64) {
        // 8乡镇初中，64城区初中
        params.period = 3
      }else if (entry2 === '123' || entry2 === '129') {
        // 8乡镇初中，64城区初中
        params.period = 2
      }else if (entry2 === '124' || entry2 === '130') {
        // 8乡镇初中，64城区初中
        params.period = 3
      }
      // 进行学校性质转换
      if (this.tpAndSchoolForm.entry1 === '116') {
        // 新城区
        params.nature = 3;
      } else if (this.tpAndSchoolForm.entry1 === '117'){
        //工业区
        params.nature = 4;
      } else if (this.tpAndSchoolForm.entry1 === '232') {
        // 区外转学 - 设置为城区学校
        params.nature = 2;
        // 确保学段为初中
        params.period = 3;
      }

      schoolList(params).then(res => {
        this.adSchoolList = res.records
        this.adSchoolLoad = false
      }).catch(() => {
        this.adSchoolLoad = false
      })
    },
    // 报名入口是否报名时间内1
    isInTime1(curTp) {
      if (curTp == 2 || curTp == 3 || curTp === '116' || curTp === '117' ) {
        this.getEntryTp2(curTp)
      } else if (curTp == 7 || curTp == 8 || curTp == 63 || curTp == 64 || curTp === '123' || curTp === '124' || curTp === '129' || curTp === '130') {
        this.getEntryTp3(curTp)
        if (this.curRole == 'COUNTY_ADMIN') {
          this.getSchoolList()
        }
      } else if (curTp == '232') { // 区外转学
        // 区外转学直接设置初中学段
        this.tpAndSchoolForm.entry2 = '8';
        // 直接使用区外转学ID获取报名类别，而不是使用学段ID
        this.getEntryTp3('232')
        if (this.curRole == 'COUNTY_ADMIN') {
          this.getSchoolList()
        }
      }
    },
    // 打开弹窗
    openTpSelect() {
      // this.tpAndSchoolForm.entry2 = ''
      this.tpAndSchoolForm.entry3 = ''
      this.tpAndSchoolForm.school = ''
      // this.entry2List = []
      this.adSchoolList = []
      if (this.curRole == 'COUNTY_ADMIN') {
        this.entry3List = []
        this.getEntryTp2(this.tpAndSchoolForm.entry1)
      }
      this.switchModal("tpAndSchoolSelect", true)
    },
    // 取消弹窗
    cancelModal() {
      // 如果已加载了表单，仅关闭弹窗
      if (this.originList.length > 0) {
        this.switchModal("tpAndSchoolSelect", false)
      } else {
        // 否则返回
        this.$router.go(-1)
      }
    },
    // 确定关闭弹窗并加载报名表单
    confirmAndQryForm() {
      this.$refs['tpAndSchoolForm'].validate((valid) => {
        if (valid) {
          this.switchModal("tpAndSchoolSelect", false)
          // 如果区县管理员
          if (this.curRole == 'COUNTY_ADMIN') {
            this.adSchool.deptName = this.adSchoolList.find(v => v.id == this.tpAndSchoolForm.school).deptName
            this.adSchool.id = this.tpAndSchoolForm.school
            this.schoolParams4Select.schoolId = this.tpAndSchoolForm.school
          } else {
            this.schoolParams4Select.schoolId = this.adSchool.id
          }
          if(this.$route.query.transferType!="区外转学报名添加"){
            // 判断学段是否为初中，如果是则设置schoolType为1
            const entry2 = this.tpAndSchoolForm.entry2
            if (entry2 == 8 || entry2 == 64 || entry2 === 124 || entry2 === 130) {
              this.schoolType = 1
            } else {
              this.schoolType = 0
            }
          }
          // 是否随迁类型
          this.sq.isSQ = this.sq.idList.some(v => v == this.tpAndSchoolForm.entry3)
          // 更新额外参数
          this.schoolParams4Select.deptCode = this.prefixDeptCode
          this.schoolParams4Select.nature = this.tpAndSchoolForm.entry1 - 1
          this.loadingAdForm = true
          this.getAdForm()
        } else {
          return false
        }
      })
    },
    // 获取报名字段
    getAdForm() {
      console.log(this.tpAndSchoolForm.entry3+"setuo")
      qryAdFormByEntry({
        key: this.tpAndSchoolForm.entry3
      }, this.prefixDeptCode).then(res => {
        // 按typeConfigId从小到大排序
        let resCopy = JSON.parse(JSON.stringify(res)).sort((a, b) => a.typeConfigId - b.typeConfigId)
        // 分类
        this.originList = resCopy.map(this.separateImgAndNormal)
        // 是随迁
        if (this.sq.isSQ) {
          // 排除经商5和务工6
          let normalIdList = [1, 2, 4, 7,25].map(v => `${ v }`)
          this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
          this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6)
          // 如果配置了经商务工
          if (this.sq.allList.length > 0) {
            this.sq.tp = this.sq.allList[0].typeConfigId
            this.sq.tpCn = this.sq.allList[0].infoName
            this.sqTpChange(this.sq.tpCn)
          }
        } else {
          let normalIdList = [1, 2, 4, 5, 6, 7,25].map(v => `${ v }`)
          this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
        }
        // 房产字段：typeConfigId >= 8但小于18
        this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
        // 如果有房产
        if (this.propertyForm.list.length > 0) {
          // 房产默认选中第1个tab
          this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
          this.propertyForm.lastTp = this.propertyForm.tp
          this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
          // 添加第1个tab的验证规则
          this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
        }
        // 优抚类型
        this.youFuForm.list = this.originList.filter(v => v.typeConfigId >= 20 &&  v.typeConfigId < 25)
        if (this.youFuForm.list.length > 0) {
          // 优抚默认选中第1个tab
          this.youFuForm.tp = this.youFuForm.list[0].typeConfigId
          this.youFuForm.lastTp = this.youFuForm.tp
          this.youFuForm.curList = this.youFuForm.list.filter(v => v.typeConfigId == this.youFuForm.tp)
          // 添加第1个tab的验证规则
          this.youFuForm.list.filter(v => v.typeConfigId == this.youFuForm.tp).map(this.addValidRules)
        }
        // 双胞胎字段：typeConfigId为3和19
        this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
        // 其他补充信息
        this.others.list = this.originList.filter(v => v.typeConfigId == 18)
        if (this.others.list.length > 0) {
          this.others.list.map(this.addValidRules)
        }
        // 加载完成
        this.loadingAdForm = false
        // 页面加载完成后，执行隐藏操作
        this.hideDisabilityCertificateUpload();
        // 初始化长幼随学字段状态
        this.initSiblingFieldsState();
        // 初始化居住证字段状态
        this.initResidenceFieldsState();
      })
    },
    // 提交
    submitAdForm() {
      // 处理表单提交前的验证规则
      this.beforeSubmitForm();
      
      // 默认第1次报名
      let params = {
        setUpSaveIds: this.tpAndSchoolForm.entry3,
        enrollSchoolId: this.adSchool.id,
        enrollSchoolName: this.adSchool.deptName,
        enrollMiddleFieldFormList: this.originList,
        houseType: this.propertyForm.tp,
        followWorkType: '',
        careType:this.youFuForm.tp,
        isTransfer: this.$route.query.transferType === "区外转学报名添加" ? 1 : 0,
        schoolType:this.schoolType,
      }
      // 检查居住证相关字段
      if (this.residenceType === '0') { // 居住证类型
        const residenceInfo = this.originList.find(item => item.typeConfigId == 7); // 居住证信息
        if (residenceInfo) {
          // 检查派出所字段（fieldId 143）
          const policeStationField = residenceInfo.leafFieldInfos.find(field => Number(field.fieldId) === 143);
          if (!policeStationField?.fieldValue || policeStationField.fieldValue.trim() === '') {
            this.$message.error("居住证类型必须填写发证派出所！");
            this.submitDisable = false;
            return false;
          }

          // 检查取证日期字段（fieldId 144）
          const certDateField = residenceInfo.leafFieldInfos.find(field => Number(field.fieldId) === 144);
          if (!certDateField?.fieldValue || certDateField.fieldValue.trim() === '') {
            this.$message.error("居住证类型必须填写取得日期！");
            this.submitDisable = false;
            return false;
          }
        }
      }

      // 检查残疾儿童证明
      if (this.isDisabled) {
        const basicInfo = this.originList.find(item => item.typeConfigId === "1");
        if (basicInfo) {
          // 查找残疾证明字段
          const disabilityCertField = basicInfo._imgItem?.find(img => img.fieldEnglish === "reserveImg1");
          if (!disabilityCertField?.fieldValue ||
              (Array.isArray(disabilityCertField.fieldValue) && disabilityCertField.fieldValue.length === 0)) {
            this.$message.error("您已选择是残疾儿童，必须上传残疾证明！");
            this.submitDisable = false;
            return false;
          }
        }
      }
      // 没选双胞胎就去掉上传字段
      if (!this.siblings.isHaveSib) {
        params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
      } else if (this.siblings.list.length == 1) {
        // 只有一条时删掉id是19的
        params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 19)
      }

      if(!this.checkDivorced()){
        this.submitDisable = false;
        return false
      }
      // 是随迁传入选中的随迁类型
      if (this.sq.isSQ) {
        params.followWorkType = this.sq.tp
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          addAd(params, this.prefixDeptCode).then(() => {
            this.$message.success('报名成功')
            this.$router.go(-1)
          })
        } else {
          return false
        }
      })
    },
    // 返回
    _goBack() {
      this.$router.go(-1)
    },
    shouldShowSiblingField(fieldId) {
      // 兄长姓名（fieldId 702）和兄长身份证号（fieldId 703）只有在选择"是"时才显示
      if (fieldId == 702 || fieldId == 703) {
        return this.isFollowSibling === '1';
      }
      // 其他字段正常显示
      return true;
    },
    // 判断是否显示居住证相关字段
    shouldShowResidenceField(fieldId) {
      // 派出所（fieldId 143）和取证日期（fieldId 144）只有在证件类型为"居住证"时才显示
      if (Number(fieldId) === 143 || Number(fieldId) === 144) {
        return this.residenceType === '0'; // '0'表示居住证，显示这两个字段
      }
      // 其他字段正常显示
      return true;
    },
    checkDivorced() {
      // Implement the logic to check if the divorced condition is met
      // This is a placeholder and should be replaced with the actual logic
      return true; // Placeholder return, actual implementation needed
    },
    // 选择框值改变
    selectValueChange(v) {
      // 处理长幼随学字段变化
      if (v.id == 701) {
        this.isFollowSibling = v.val;
        
        // 如果切换为"否"，清空兄长姓名和兄长身份证号字段的值
        if (v.val === '0') {
          this.$nextTick(() => {
            // 清空字段值
            this.$set(this.form, '702', '');
            this.$set(this.form, '703', '');
            
            // 更新originList中的字段值
            this.originList.forEach(item => {
              item.leafFieldInfos.forEach(field => {
                if (field.fieldId == 702 || field.fieldId == 703) {
                  field.fieldValue = '';
                  // 如果有DOM引用，也更新DOM值
                  if (this.$refs[field.fieldId] && this.$refs[field.fieldId][0]) {
                    this.$refs[field.fieldId][0].valChange('');
                  }
                }
              });
            });
            
            // 清除验证状态
            if (this.$refs.form) {
              this.$refs.form.clearValidate(['702', '703']);
            }
          });
        }
        
        // 仅更新UI显示，延迟更新验证规则
        this.$nextTick(() => {
          // 先清除可能的验证状态
          if(this.$refs.form) {
            this.$refs.form.clearValidate(['702', '703']);
          }
        });
      }

      this.changeDivorcedVal(v.id, v.val);
      this.changeVal(v.id, v.val);
      
      if(this.prefixDeptCode=='130209' || this.prefixDeptCode=='130204') {
        if (v.id === 7 && v.val === '1') {
          this.isDisabled = true;
          const basicInfo = this.originList.find(item => item.typeConfigId === "1");
          const basicInfoGroup = basicInfo._imgItem?.find(img => img.fieldEnglish === "reserveImg1");
          basicInfoGroup.isVisible = this.isDisabled ? '' : 'none';
        } else if (v.id === 7 && v.val === '0') {
          this.isDisabled = false;
        }
      }
      
      if (v.id == 350 || String(v.id) === '350') {
        this.isLuanzhouGraduate = v.val
      }
      // 处理居住证类型字段变化
      if (v.id == 700) {
        this.residenceType = v.val;
        this.updateResidenceCertLabel(v.val);
        // 同时更新派出所和取证日期字段的显示状态
        this.updateResidenceFieldsVisibility(v.val);
      }
    },
    updateResidenceCertLabel(residenceType) {
      // 查找居住证编号字段（fieldId 57）
      this.originList.forEach(item => {
        if (item.typeConfigId == 7) { // 居住证信息
          item._normalItem.forEach(field => {
            if (field.fieldId == 57) { // 居住证编号字段
              if (residenceType == '1') {
                field.fieldName = '居住凭证编号'
              } else {
                field.fieldName = '居住证编号'
              }
            }
          })
        }
      })
      // 强制更新视图
      this.$forceUpdate()
    },
    // 更新居住证相关字段的显示状态
    updateResidenceFieldsVisibility(residenceType) {
      this.residenceType = residenceType;

      // 如果切换为居住凭证，清空派出所和取证日期字段的值
      if (residenceType === '1') {
        this.$nextTick(() => {
          // 清空字段值
          this.$set(this.form, '143', '');
          this.$set(this.form, '144', '');

          // 更新originList中的字段值
          this.originList.forEach(item => {
            if (item.typeConfigId == 7) { // 居住证信息
              item.leafFieldInfos.forEach(field => {
                if (field.fieldId == 143 || field.fieldId == 144) {
                  field.fieldValue = '';
                  // 如果有DOM引用，也更新DOM值
                  if (this.$refs[field.fieldId] && this.$refs[field.fieldId][0]) {
                    this.$refs[field.fieldId][0].valChange('');
                  }
                }
              });
            }
          });

          // 清除验证状态
          if (this.$refs.form) {
            this.$refs.form.clearValidate(['143', '144']);
          }
        });
      }
    },
    updateSiblingFieldsDisplay(isFollowSibling) {
      // 只处理字段显示/隐藏，不触发验证
      // 在表单提交时会统一验证
      
      // 更新字段值
      this.isFollowSibling = isFollowSibling;
      
      // 在提交表单时才处理验证规则
      this.$nextTick(() => {
        // 先清除可能的验证状态
        if(this.$refs.form) {
          this.$refs.form.clearValidate(['702', '703']);
        }
      });
      
      // 动态添加或移除验证规则，但推迟到表单提交时
      if (isFollowSibling === '1') {
        // 只在提交时添加验证规则
        this.$nextTick(() => {
          // 延迟添加验证规则
          this.$set(this.rules, '702', {
            required: true,
            trigger: 'submit', // 只在提交时触发
            message: '请输入兄长姓名'
          });
          this.$set(this.rules, '703', {
            required: true,
            trigger: 'submit', // 只在提交时触发
            validator: (rule, value, callback) => {
              if (!value) {
                return callback(new Error('请输入兄长身份证号'));
              }
              const idCardReg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/;
              if (idCardReg.test(value)) {
                callback();
              } else {
                callback(new Error('请输入正确格式的身份证号'));
              }
            }
          });
        });
      } else {
        // 延迟移除验证规则和清空值
        this.$nextTick(() => {
          // 移除验证规则
          this.$delete(this.rules, '702');
          this.$delete(this.rules, '703');
          // 清空字段值
          this.$set(this.form, '702', '');
          this.$set(this.form, '703', '');
          // 同时更新originList中的字段值
          this.originList.forEach(item => {
            item.leafFieldInfos.forEach(field => {
              if (field.fieldId == 702 || field.fieldId == 703) {
                field.fieldValue = '';
                // 如果有DOM引用，也更新DOM值
                if (this.$refs[field.fieldId] && this.$refs[field.fieldId][0]) {
                  this.$refs[field.fieldId][0].valChange('');
                }
              }
            });
          });
        });
      }
    },
    initSiblingFieldsState() {
      // 查找是否长幼随学字段的当前值
      this.originList.forEach(item => {
        item.leafFieldInfos.forEach(field => {
          if (field.fieldId == 701) {
            this.isFollowSibling = field.fieldValue || '0';
            // 根据当前值设置字段显示状态，延迟到DOM更新后
            this.$nextTick(() => {
              this.updateSiblingFieldsDisplay(this.isFollowSibling);
            });
          }
        });
      });
    },
    // 初始化居住证字段状态
    initResidenceFieldsState() {
      // 查找证件类型字段的当前值
      let foundResidenceType = false;
      this.originList.forEach(item => {
        if (item.typeConfigId == 7) { // 居住证信息
          item.leafFieldInfos.forEach(field => {
            if (field.fieldId == 700) { // 证件类型
              this.residenceType = field.fieldValue || '0'; // 默认为居住证
              foundResidenceType = true;
            }
          });
        }
      });

      // 如果没有找到证件类型字段，使用默认值
      if (!foundResidenceType) {
        this.residenceType = '0'; // 默认为居住证
      }
    },
    beforeSubmitForm() {
      // 根据是否长幼随学更新验证规则
      if (this.isFollowSibling === '1') {
        // 添加验证规则
        this.$set(this.rules, '702', {
          required: true,
          trigger: 'submit',
          message: '请输入兄长姓名'
        });
        this.$set(this.rules, '703', {
          required: true,
          trigger: 'submit',
          validator: (rule, value, callback) => {
            if (!value) {
              return callback(new Error('请输入兄长身份证号'));
            }
            const idCardReg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/;
            if (idCardReg.test(value)) {
              callback();
            } else {
              callback(new Error('请输入正确格式的身份证号'));
            }
          }
        });
      } else {
        // 移除验证规则
        this.$delete(this.rules, '702');
        this.$delete(this.rules, '703');
      }

      // 根据居住证类型更新验证规则
      if (this.residenceType === '0') { // 居住证类型
        // 添加派出所和取证日期的验证规则
        this.$set(this.rules, '143', {
          required: true,
          trigger: 'submit',
          message: '请输入居住证发证派出所'
        });
        this.$set(this.rules, '144', {
          required: true,
          trigger: 'submit',
          message: '请选择居住证取得日期'
        });
      } else {
        // 移除验证规则
        this.$delete(this.rules, '143');
        this.$delete(this.rules, '144');
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.ad-form {
  background-color: #FFF;
  padding: 10px;
  .common-title {
    margin-bottom: 10px;
  }
  .ad-form-actions {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>