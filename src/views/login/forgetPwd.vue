<template>
  <div class="container-box">
    <div class="title">唐山市教育入学一件事服务平台管理后台</div>
    <div class="login-box">
      <div class="login-title">
        <el-button
          type="text"
          @click="back"
          class="back"
          icon="el-icon-back"
          size="small"
          >返回</el-button
        >
        忘记密码
      </div>
      <div class="line"></div>
      <el-form :model="form" :rules="rules" ref="form" v-show="!showJumpTips">
        <el-form-item label="账号" :label-width="labelW" prop="loginName">
          <el-input
            v-model.trim="form.loginName"
            placeholder="请输入账号"
            prefix-icon="el-icon-user"
            :style="formItemW"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号" :label-width="labelW" prop="phoneNumber">
          <el-input
            v-model.trim="form.phoneNumber"
            prefix-icon="el-icon-user"
            placeholder="请输入手机号"
            oninput="value = value.replace(/[^\d]/g,'')"
            :style="formItemW"
            maxlength="11"
          ></el-input>
          <el-button
            type="text"
            @click="getSms"
            class="get-sms"
            size="mini"
            ref="getSmsBtn"
            :disabled="disableSmsBtn"
            >获取验证码</el-button
          >
        </el-form-item>
        <el-form-item label="短信验证码" :label-width="labelW" prop="smsYzm">
          <el-input
            v-model.trim="form.smsYzm"
            prefix-icon="el-icon-message"
            placeholder="请输入短信验证码"
            oninput="value = value.replace(/[^\d]/g,'')"
            :style="formItemW"
            ref="checkCode"
            maxlength="6"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" :label-width="labelW" prop="newPassword">
          <el-input
            v-model="form.newPassword"
            prefix-icon="el-icon-lock"
            placeholder="请输入新密码"
            show-password
            :style="formItemW"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="确认新密码"
          :label-width="labelW"
          prop="confirmPassword"
        >
          <el-input
            v-model="form.confirmPassword"
            prefix-icon="el-icon-lock"
            placeholder="再次输入新密码"
            show-password
            :style="formItemW"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-button
        class="sd-m-t-20"
        type="primary"
        @click="submitForm"
        style="width: 100%"
        :loading="loading"
        v-show="!showJumpTips"
        >确&nbsp;&nbsp;定</el-button
      >
      <div class="jump-tips-box" v-show="showJumpTips">
        <p class="jump-tips">
          密码修改成功，<span class="jump-remain-sec" ref="remainSec">{{
            countdown.jump
          }}</span
          >秒后跳转到登录页面
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { forgetPwd, getSmsCheckCode } from "@/api/login";
export default {
  data() {
    var validPass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("密码不能为空"));
      } else {
        let reg =
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,12}$/;
        if (reg.test(value)) {
          callback();
        } else {
          return callback(
            new Error("密码必须由 “字母+数字+特殊符号!@#$%^&*” 组成，6~12位")
          );
        }
      }
    };
    var validSurePass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("确认密码不能为空"));
      } else {
        let reg =
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,12}$/;
        if (reg.test(value)) {
          if (this.form.newPassword) {
            if (value == this.form.newPassword) {
              callback();
            } else {
              return callback(new Error("两次输入的密码不一致"));
            }
          }
        } else {
          return callback(
            new Error("密码必须由 “字母+数字+特殊符号!@#$%^&*” 组成，6~12位")
          );
        }
      }
    };
    return {
      loading: false,
      countdown: {
        jump: 3,
        sms: 300,
      },
      disableSmsBtn: true,
      timer: {
        jump: 0,
        sms: 0,
      },
      labelW: "95px",
      formItemW: {
        width: "220px",
      },
      showJumpTips: false,
      form: {
        phoneNumber: "",
        smsYzm: "",
        loginName: "",
        newPassword: "",
        confirmPassword: "",
      },
      rules: {
        phoneNumber: [
          {
            required: true,
            message: "手机号格式不正确",
            trigger: "blur",
            validator: (rule, val, callback) => {
              return /^1[23456789]\d{9}$/.test(val)
                ? ""
                : callback(new Error("手机号格式不正确"));
            },
          },
        ],
        smsYzm: [
          {
            required: true,
            message: "验证码不能为空",
            trigger: "blur",
            validator: (rule, val, callback) => {
              return /^\d{6}$/.test(val)
                ? ""
                : callback(new Error("验证码格式不正确"));
            },
          },
        ],
        loginName: [
          { required: true, message: "账号不能为空", trigger: "blur" },
        ],
        newPassword: [{ required: true, validator: validPass, trigger: "blur" }],
        confirmPassword: [
          { required: true, validator: validSurePass, trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    "form.phoneNumber"(newV) {
      newV = `${newV}`;
      this.disableSmsBtn = true;
      if (newV.length == 11) {
        this.disableSmsBtn = false;
      }
    },
  },
  methods: {
    // 获取验证码
    getSms() {
      let that = this;
      getSmsCheckCode({
        phoneNumber: this.form.phoneNumber,
      }).then(() => {
        that.$refs.checkCode.focus();
        that.form.smsYzm = "";
        that.disableSmsBtn = true;
        that.startCountdown(
          that.timer.sms,
          that.countdown.sms,
          that.$refs.getSmsBtn,
          "s后重新获取",
          () => {
            that.disableSmsBtn = false;
            that.$refs.getSmsBtn.$el.innerText = "重新发送";
          }
        );
      });
    },
    // 提交
    submitForm() {
      let that = this;
      forgetPwd(this.form).then((res) => {
        this.$message({
          message: "密码修改成功",
          onClose() {
            that.showJumpTips = true;
            that.startCountdown(
              that.timer.jump,
              that.countdown.jump,
              that.$refs.remainSec,
              "",
              () => {
                clearTimeout(that.timer.jump);
                clearTimeout(that.timer.sms);
                that.$router.push("/login");
              }
            );
          },
        });
      });
    },
    // 倒计时
    // 定时器，总秒数，递减变量，显示文字 计时完毕的callback
    startCountdown(timer, vari, ref, extraTxt, callback) {
      clearTimeout(timer);
      let that = this;
      let el = ref.$el ? ref.$el : ref;
      timer = setTimeout(function () {
        vari--;
        el.innerText = `${vari}${extraTxt}`;
        if (vari == 0) {
          clearTimeout(timer);
          callback && callback();
        } else {
          that.startCountdown(timer, vari, ref, extraTxt, callback);
        }
      }, 1000);
    },
    // 返回
    back() {
      clearTimeout(this.timer.jump);
      clearTimeout(this.timer.sms);
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.container-box {
  width: 100%;
  height: 100vh;
  position: relative;
  padding-top: 50px;
  box-sizing: border-box;
  background: url("../../assets/images/login.jpg") no-repeat center / 100% 100%;
  .title {
    margin-top: 42px;
    text-align: center;
    color: white;
    font-size: 50px;
    text-shadow: 3px 3px 3px rgba(148, 150, 150, 0.99);
  }
}
.login-box {
  width: 480px;
  padding: 20px 40px;
  background-color: #fff;
  border-radius: 5px;
  transform: translate(-50%, -50%);
  box-sizing: border-box;
  position: relative;
  top: 32%;
  left: 50%;
  .login-title {
    color: #305893;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    padding-bottom: 15px;
    position: relative;
    .back {
      position: absolute;
      left: 0;
    }
  }
  .line {
    border-bottom: 1px solid #e2e3e7;
    margin-bottom: 22px;
  }
  .get-sms {
    padding-left: 15px;
  }
}
</style>
