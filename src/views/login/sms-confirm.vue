<template>
  <div class="sd-wechat-container">
    <div class="sd-qrcode-container">
      <div class="sd-title">短信验证码确认</div>
			<el-form :model="loginForm" ref="loginForm">
				<el-form-item prop="phoneNumber" label="登录用户">{{ phoneFmt }}</el-form-item>
				<el-form-item prop="sms">
					<div class="sms-wrap">
						<el-input size="small" ref="smsInput" v-model="loginForm.smsYzm" placeholder="请输入验证码" maxlength="6" class="sms-input"></el-input>
						<el-button size="small" type="primary" plain :disabled="smsCfg.confirmBtnDisable" @click="getSms">{{ smsCfg.confirmBtnTxt }}</el-button>
						<timer ref="smsTimer" :total="smsCfg.timerTotalSec" @start="smsStart" @ongoing="smsOngoing" @end="smsEnd"></timer>
					</div>
				</el-form-item>
			</el-form>
			<div class="actions">
				<el-button
				  type="primary"
				  size="small"
				  icon="el-icon-back"
				  plain
				  @click="back"
				  >返回</el-button
				>
				<el-button
					type="primary"
					size="small"
					@click="confirmLogin"
				>确认</el-button>
			</div>
    </div>
  </div>
</template>

<script>
import Timer from "@/components/timer.vue"
import { getSms, logout } from "@/api/login.js"
export default {
	components: {
		Timer
	},
  data() {
    return {
			phone: this.$store.getters.mobile,
			loginForm: {
				phoneNumber: '',
				smsYzm: '',
				isParent: 0
			},
			showLogin: false,
			smsCfg: {
				// 倒计时时长，单位秒
				timerTotalSec: 60,
				// 默认禁用确定按钮
				confirmBtnDisable: false,
				// 确定按钮txt
				confirmBtnTxt: '获取验证码'
			},
    }
  },
	computed: {
		// 格式化手机号
		phoneFmt() {
			return `${ this.phone.slice(0, 3) }****${ this.phone.slice(-4) }`
		}
	},
	beforeRouteLeave(to, from, next) {
		if (to.path !== '/login') {
			next()
		} else {
			if (to.path == '/login') {
				this.$store.dispatch("user/logout").then(() => {
				  next()
				})
			} else {
				next('/login')
			}
		}
	},
  created() {
    const token = this.$store.getters.token
    if (!token) {
      this.$router.replace("/login")
    } else {
			this.loginForm.phoneNumber = this.phone
		}
  },
  methods: {
    back() {
      this.$store.dispatch("user/logout").then(() => {
        this.$router.replace("/login")
      })
    },
    // 获取验证码
    getSms() {
    	getSms({ 
    		phoneNumber: this.loginForm.phoneNumber
    	}).then(res => {
    		this.loginForm.smsYzm = ''
    		this.smsCfg.confirmBtnDisable = true
    		this.$refs['smsTimer']._startTimer()
    	})
    },
    // 验证码开始
    smsStart() {
    	this.smsCfg.confirmBtnTxt = `${ this.smsCfg.timerTotalSec }秒后重新获取`
    },
    // 验证码进行中
    smsOngoing(remain) {
    	this.smsCfg.confirmBtnTxt = `${ remain }秒后重新获取`
    },
    // 验证码结束
    smsEnd() {
    	this.smsCfg.confirmBtnTxt = `重新获取`
    	this.smsCfg.confirmBtnDisable = false
    },
		// 确认登录
		confirmLogin() {
			let reg = /^\d{6}$/
			if (reg.test(this.loginForm.smsYzm)) {
				this.$store.dispatch("user/login", this.loginForm).then(res => {
					this.$router.push("/")
				}).catch(() => { 
					this.loginForm.smsYzm = ''
				})
			} else {
				this.$message.warning('验证码格式不正确')
			}
		}
  },
}
</script>

<style lang="scss" scoped>
.sd-wechat-container {
  height: 100vh;
  width: 100%;
  background-color: #8c939d;
  display: flex;
  justify-content: center;
  align-items: center;

  .sd-qrcode-container {
    width: 500px;
    height: 310px;
    background-color: #eeeeee;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    border-radius: 10px;

    .sd-title {
      height: 120px;
      line-height: 120px;
      font-size: 24px;
      font-weight: bold;
    }

    .sd-warning-text {
      position: absolute;
      top: 450px;
    }
  }
	.sms-wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.sms-input {
			margin-right: 15px;
		}
	}
	.actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}
</style>