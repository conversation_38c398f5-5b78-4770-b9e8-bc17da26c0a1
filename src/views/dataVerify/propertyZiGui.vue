property.vue<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportList"
          >导出房管信息</el-button
        >
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="openImport"
          >导入房管信息</el-button
        >
      </div>
      <div class="search-form_right">
        <el-form :model="search" :inline="true">
          <el-form-item prop="enrollId">
            <el-input
              size="small"
              v-model.trim="search.enrollId"
              placeholder="报名ID"
              style="width: 185px"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="keywords">
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="姓名或身份证"
              style="width: 185px"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="createdTime">
            <el-date-picker
              size="small"
              v-model="search.createdTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="报名开始日期"
              style="width: 185px"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime">
            <el-date-picker
              size="small"
              v-model="search.endTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="报名结束日期"
              style="width: 185px"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="search.ziGuiReviewStatus"
              placeholder="资规审核状态"
              style="width: 185px"
              clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名ID"
        width="150"
        prop="enrollId"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学段"
        prop="enrollStageText"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="身份证号"
        prop="studentIdCardNumber"
        width="180"
      ></el-table-column>
      <el-table-column
        align="center"
        label="类别"
        prop="type"
        width="300"
      ></el-table-column>
      <el-table-column
        align="center"
        label="证件编号"
        prop="certificateCode"
        width="240"
      ></el-table-column>
      <el-table-column
        align="center"
        label="户主"
        prop="householdName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="房主姓名"
        prop="householdName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="与该生关系"
        prop="relationOfStudent"
      >
        <template slot-scope="scope">
          {{ relationCn(scope.row.relationOfStudent) }}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="资规审核状态"
          prop="ziGuiReviewStatus"
      >
        <template slot-scope="scope">
          {{ ziGuiReviewStatusCn(scope.row.ziGuiReviewStatus) }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        label="操作"
        width="300px"
        v-if="role == 'HOUSE'"
      >
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-view"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="detail(row)"
            >详情</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.publicSecurityReviewStatus == '通过'"
            @click="pass(row)"
            >通过</el-link
          >
          <el-link
            icon="el-icon-close"
            type="danger"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.publicSecurityReviewStatus == '不通过'"
            @click="fail(row)"
            >不通过</el-link
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 导入 -->
    <el-dialog
      :visible.sync="modal.import"
      center
      title="导入信息"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-upload
        action="#"
        :limit="upload.limit"
        :auto-upload="upload.auto"
        :file-list="upload.list"
        :on-change="changeFile"
        :on-remove="removeFile"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>
      <div slot="footer">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :disabled="upload.list.length == 0"
          @click="confirmUpload"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 详情 -->
    <!-- <el-dialog
      :visible.sync="modal.detail"
      center
      title="报名详情"
      :close-on-click-modal="false"
      width="500px"
    >
      <div>报名详情在这</div>
      <div slot="footer">
        <el-button size="small" @click="switchModal('detail', false)"
          >取消</el-button
        >
        <el-button size="small" type="success" @click="confirmPass"
          >通过</el-button
        >
        <el-button size="small" type="danger" @click="confirmFail"
          >不通过</el-button
        >
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {propDeptList, propDeptIm, importZiGuiReviewStatus} from "@/api/adInfo";
import { relationList } from "@/utils/dictionary";
import { pref } from "@/utils/common";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      role: this.$store.getters.role,
      search: {
        enrollId: "",
        keywords: "",
        createdTime: "",
        endTime: "",
        publicSecurityAndHouseReviewStatus: "",
        type: 1,
        ziGuiReviewStatus:''
    },
      modal: {
        import: false,
        // detail: false,
      },
      upload: {
        limit: 1,
        auto: false,
        list: [],
      },
    };
  },
  methods: {
    // 资规审核状态转换
    ziGuiReviewStatusCn(val) {
      const statusMap = {
        1: '待审核',
        2: '通过',
        3: '不通过'
      };
      return statusMap[val] || '';
    },
    // 关系
    relationCn(val) {
      return relationList.find((v) => v.id == val)?.val || "";
    },
    // 导出
    exportList() {
      let params = { ...this.search };
      // if (this.prefixDeptCode != "130427") {
      this.$download(
        `${pref}${this.prefixDeptCode}/biz/excelConfig/exportExcelPublicHouse`,
        params,
        "xls",
        "房管信息列表.xls"
      ).then((res) => {
        this.$message.success("导出成功");
      });
      // } else {
      //   // 磁县130427
      //   this.$download(
      //     `${pref}${this.prefixDeptCode}/biz/house/property/cxExportHouse`,
      //     params,
      //     "xls",
      //     "房管信息列表.xls"
      //   ).then((res) => {
      //     this.$message.success("导出成功");
      //   });
      // }
    },
    // 导入
    openImport() {
      this.switchModal("import", true);
      this.removeFile();
    },
    // 列表
    getTableData() {
      propDeptList(this.search, this.prefixDeptCode).then(
        (data) => (this.tableData = data)
      );
    },
    // 上传
    changeFile(file, fileList) {
      this.upload.list = [file.raw];
    },
    // 删除上传
    removeFile() {
      this.upload.list = [];
    },
    // 上传
    confirmUpload() {
      if (!this.upload.list || this.upload.list.length === 0) {
        this.$message.error("请先选择文件");
        return;
      }

      let fd = new FormData();
      // 使用最后一个文件（最新选择的）
      const currentFile = this.upload.list[this.upload.list.length - 1];
      fd.append("file", currentFile.raw || currentFile);

      importZiGuiReviewStatus(fd, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.switchModal("import", false);
        this.getTableData();
        this.removeFile(); // 清空文件列表
      }).catch((error) => {
        console.error("上传失败:", error);
      });
    },
    // 详情
    detail() {
      this.switchModal("detail", true);
    },
    // 通过
    pass(row) {
      // enableSchool({
      //   key: row.id,
      // }).then((res) => {
      //   this.$message.success("操作成功");
      //   this.getTableData();
      // });
    },
    // 不通过
    fail(row) {},
    // 详情-通过
    confirmPass() {},
    // 详情-不通过
    confirmFail() {},
  },
};
</script>

<style lang="scss">
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-form--inline .el-form-item {
    margin-bottom: 0 !important;
  }
}
</style>
