<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div>
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportStatistics"
          :loading="exportLoading"
          >导出</el-button
        >
      </div>
      <div>
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-select
              size="small"
              v-model="search.nature"
              placeholder="学校性质"
              clearable
            >
              <el-option label="乡镇学校" :value="1"></el-option>
              <el-option :label="$store.getters.deptCode === '130209'?'主城区学校':'城区学校'" :value="2" ></el-option>
              <el-option label="新城区学校" :value="3" v-if="$store.getters.deptCode === '130209'"></el-option>
              <el-option label="工业区学校" :value="4" v-if="$store.getters.deptCode === '130209'"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        align="center"
        label="序号"
        width="50"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学校性质"
        prop="natureName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名学校"
        prop="schoolName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="计划招生数"
        prop="planNum"
      ></el-table-column>
			<el-table-column
				v-for="colItem, colIdx in columnObj"
				:key="colItem.name"
			  align="center"
			  :label="colItem.nameStr"
			  :prop="colItem.name"
			></el-table-column>
      <el-table-column align="center" label="合计" prop="totalCount">
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import { schoolStatistics, schoolStatisticsColumn } from "@/api/statistics";
import { pref } from "@/utils/common";
export default {
  mixins: [TableMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      search: {
        nature: "",
        period: "2",
      },
      exportLoading: false,
      columnObj: [],
    };
  },
  methods: {
    // 列表
    async getTableData() {
      this.tableLoading = true;
      this.columnObj = await schoolStatisticsColumn(
        { period: this.search.period, nature: this.search.nature },
        this.prefixDeptCode
      );
      
      // 针对滦南地区(130224)进行报名类型列排序
      if (this.prefixDeptCode === '130224') {
        // 定义报名类型的排序映射
        const sortMapping = {
          '双统一': 1,
          '户口房产不一致': 2,
          '使用祖父母/外祖父母房产': 3,
          '随迁子女（滦南有房产）': 4,
          '滦南有户口无房产': 5,
          '随迁子女': 6
        };
        
        // 对columnObj进行排序
        this.columnObj.sort((a, b) => {
          // 获取列名对应的排序权重，如果不在映射中则给一个较大的值
          const weightA = sortMapping[a.nameStr] || 999;
          const weightB = sortMapping[b.nameStr] || 999;
          return weightA - weightB;
        });
      }
      
      schoolStatistics(this.search, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 导出
    exportStatistics() {
      this.exportLoading = true;
      let params = {
        period: this.search.period,
        nature: this.search.nature,
      };
      this.$download(
        `${pref}${this.prefixDeptCode}/biz/statistics/export`,
        params,
        "xls",
        "公办小学统计.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
