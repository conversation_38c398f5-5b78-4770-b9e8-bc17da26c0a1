<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button
            size="small"
            icon="el-icon-plus"
            type="primary"
            @click="dialogFormComponent.show('AddForm')"
        >添加
        </el-button
        >
      </div>
      <div class="search-form_right">
        <el-form :model="search" :inline="true">
          <el-form-item prop="keywords">
            <el-input
                size="small"
                v-model.trim="search.keywords"
                placeholder="学生姓名/身份证号"
                class="sd-w-200"
                clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-table :data="records" border stripe style="width: 100%">
      <el-table-column
          align="center"
          label="序号"
          width="60"
          type="index"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="idCardNumber"
      ></el-table-column>
      <el-table-column
          align="center"
          label="创建时间"
          prop="createTime"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="{ row }">
          <el-link
              icon="el-icon-edit"
              type="warning"
              :underline="false"
              style="margin-right: 10px"
              @click="dialogFormComponent.show('EditForm',row)"
          >编辑
          </el-link
          >
          <el-link
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              @click="del(row)"
          >删除
          </el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="totals > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="Number(totals)"
      >
      </el-pagination>
    </div>

    <!-- 新增，编辑 -->
    <el-dialog
        :title="dialogFormComponent.title()"
        :visible.sync="dialogFormComponent.dialogFormVisible"
        center
        width="600px"
        :close-on-click-modal="false">
      <AddForm v-if="dialogFormComponent.name==='AddForm'" ref="addForm" mode="ADD"/>
      <EditForm v-if="dialogFormComponent.name==='EditForm'" ref="editForm" :data="dialogFormComponent.data"
                mode="EDIT"/>
      <div class="flex-center">
        <el-button size="small" @click="dialogFormComponent.close()">取消
        </el-button>
        <el-button size="small" type="primary" @click="submitForm" :loading="dialogFormComponent.saveLoading">确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import TableMixin from "@/mixins/TableMixin";
import AddForm from './Form.vue'
import EditForm from './Form.vue'
import {
  getWhitelistList,
  addWhitelist,
  updateWhitelist,
  deleteWhitelist
} from "@/api/setting";
import {idCardValidator} from "@/utils/validator";

export default {
  mixins: [TableMixin],
  components: {AddForm, EditForm},
  data() {
    return {
      search: {
        keywords: "",
        pageNumber: 1,
        pageSize: 10
      },
      form: {
        id: "",
        studentName: "",
        idCardNumber: ""
      },
      rules: {
        studentName: [
          {required: true, message: "请输入学生姓名", trigger: "blur"}
        ],
        idCardNumber: [
          {required: true, message: "请输入身份证号", trigger: "blur"},
          {validator: idCardValidator, trigger: "blur"}
        ]
      },
      dialogTitle: "新增二次白名单",
      modal: {
        addOrEdit: false
      },
      records: [],
      totals: 0,
      regionCode: this.$store.getters.deptCode
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    // 获取表格数据
    getTableData() {
      console.log(" this.saveLoading", this.saveLoading);
      getWhitelistList(this.search, this.regionCode).then((res) => {
        console.log('res:', res);
        // 直接赋值，不做任何结构解析
        this.records = res.records || [];
        this.totals = Number(res.total || 0);
        console.log('赋值后 records:', this.records);
      });
    },
    // 搜索
    searchSubmit() {
      this.search.pageNumber = 1;
      this.getTableData();
    },
    // 编辑白名单
    edit(row) {
      this.dialogTitle = "编辑二次白名单";
      this.modal.addOrEdit = true;

      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields();
        this.form = {
          id: row.id,
          studentName: row.studentName,
          idCardNumber: row.idCardNumber
        };
      });
    },
    // 删除白名单
    del(row) {
      this.$confirm(`确认删除该白名单记录？`, "删除确认", {
        type: "error",
      }).then(() => {
        deleteWhitelist({key: row.id}, this.regionCode).then(() => {
          this.$message.success("删除成功");
          this.getTableData();
        });
      });
    },
    submitForm() {
      const formRef = this.dialogFormComponent.name === 'AddForm'
          ? this.$refs.addForm
          : this.$refs.editForm;
      if (!formRef) return;
      this.dialogFormComponent.saveLoading = true;
      formRef.validate((valid) => {
        if (valid) {
          const formData = formRef.form;
          if (formData.id) {
            updateWhitelist(formData, this.regionCode).then(() => {
              this.$message.success("修改成功");
              this.dialogFormComponent.close(); // 使用新的方式关闭弹窗
              this.getTableData();
            }).finally(() => this.dialogFormComponent.saveLoading = false);
          } else {
            addWhitelist(formData, this.regionCode).then(() => {
              this.$message.success("添加成功");
              this.dialogFormComponent.close(); // 使用新的方式关闭弹窗
              this.getTableData();
            }).finally(() => this.dialogFormComponent.saveLoading = false);
          }
        }
      });
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.search.pageSize = val;
      this.getTableData();
    },
    // 页码变化
    handleCurrentChange(val) {
      this.search.pageNumber = val;
      this.getTableData();
    }
  },
};
</script>
<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>