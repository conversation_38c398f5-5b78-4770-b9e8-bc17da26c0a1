<!-- 学籍管理 -->
<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          >批量导入</el-button
        >
      </div>
      <div class="sd-search">
        <el-input
          v-model.trim="search.keywords"
          placeholder="请输入姓名/身份证号/学籍号"
          size="small"
          clearable
          style="width: 240px"
        ></el-input>
        <el-select
          v-model="search.schoolId"
          placeholder="筛选学校-可模糊搜索"
          size="small"
          clearable
        >
          <el-option
            v-for="item in schoolList"
            :key="item.id"
            :label="item.deptName"
            :value="item.id"
          ></el-option>
        </el-select>
        <el-button
          type="primary"
          class="sd-btn-search"
          icon="el-icon-search"
          @click="searchSubmit"
          size="small"
        />
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column align="center" label="性别" prop="sex">
        <template slot-scope="{ row }">
          <span>{{ switchGender(row.gender) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="学生身份证号"
        prop="idCard"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学籍号"
        prop="studentCode"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        label="毕业学校"
        prop="schoolName"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column align="center" label="操作" width="360" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="edit(row, $index)"
          >编辑
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 编辑学籍信息 -->
    <el-dialog
      title="编辑学籍信息"
      :visible.sync="modal.edit"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editRules" ref="editForm" label-width="100px">
        <el-form-item label="学生姓名" prop="studentName">
          <el-input v-model="editForm.studentName" placeholder="请输入学生姓名"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="editForm.gender" placeholder="请选择性别">
            <el-option label="男" value="1"></el-option>
            <el-option label="女" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="editForm.idCard" placeholder="请输入身份证号"></el-input>
        </el-form-item>
        <el-form-item label="学籍号" prop="studentCode">
          <el-input v-model="editForm.studentCode" placeholder="请输入学籍号"></el-input>
        </el-form-item>
        <el-form-item label="毕业学校" prop="schoolId">
          <el-select v-model="editForm.schoolId" placeholder="请选择学校" @change="handleSchoolChange">
            <el-option
              v-for="item in schoolList"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('edit', false)">取消</el-button>
        <el-button size="small" type="primary" @click="editSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { getList, importStudentCode,editStudentCode} from "@/api/setting";
import { getDepts } from "@/api/common";
import { idCardValidator } from "@/utils/validator";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: null,
        schoolId: "",
      },
      schoolList: [],
      dialogTitle: "批量导入",
      modal: {
        import: false,
        edit: false,
      },
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
      editForm: {
        id: null,
        studentName: "",
        gender: "",
        idCard: "",
        studentCode: "",
        schoolId: "",
        schoolName: ""
      },
      editRules: {
        studentName: [{ required: true, message: "请输入学生姓名", trigger: "blur" }],
        gender: [{ required: true, message: "请选择性别", trigger: "change" }],
        idCard: [
          { required: true, message: "请输入身份证号", trigger: "blur" },
          { validator: idCardValidator, trigger: "blur" }
        ],
        studentCode: [{ required: true, message: "请输入学籍号", trigger: "blur" }],
        schoolId: [{ required: true, message: "请选择学校", trigger: "change" }]
      }
    };
  },
  created() {
    this.getDepts();
  },
  computed: {
    switchGender() {
      return (val) => (val == "1" ? "男" : val == "2" ? "女" : "");
    },
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getList(this.search).then((res) => {
        this.tableData = res;
        this.tableLoading = false;
      });
    },
    // 获取区县下的学校列表
    getDepts() {
      let params = {
        level: 3,
        period: "2",
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        "/user-api/center/studentCode/downTemplate",
        {},
        "xls",
        "毕业小学学籍.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importStudentCode(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
    // 编辑学籍信息
    edit(row, index) {
      this.editForm = {
        id: row.id,
        studentName: row.studentName,
        gender: row.gender,
        idCard: row.idCard,
        studentCode: row.studentCode,
        schoolId: row.schoolId,
        schoolName: row.schoolName
      };
      this.switchModal("edit", true);
    },
    // 学校选择变更
    handleSchoolChange(val) {
      const selectedSchool = this.schoolList.find(item => item.id === val);
      if (selectedSchool) {
        this.editForm.schoolName = selectedSchool.deptName;
      }
    },
    // 提交编辑
    editSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          editStudentCode(this.editForm).then(res => {
            if(res){
              this.$message.success("编辑成功");
            }else{
            this.$message.success("编辑失败");
            }
            this.switchModal("edit", false);
            this.getTableData();
          });
        }
      });
    }
  },
};
</script>

<style scoped>
.warning-desc-text {
  color: #e6a23c;
}
.error-desc-text {
  color: #f56c6c;
}
</style>