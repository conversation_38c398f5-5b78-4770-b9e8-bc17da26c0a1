<template>
  <div>
    <ul class="setting-list">
      <li v-for="(item, idx) in dataList" :key="item.type">
        <el-card class="box-card">
          <div slot="header">
            <span>{{ item.name }}</span>
          </div>
          <el-form :model="item" label-width="100px">
            <el-form-item prop="beginTime" label="开始时间">
              <el-date-picker
                  size="small"
                  v-model="item.beginTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择开始时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="confirmStart(item, idx)"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="endTime" label="结束时间">
              <el-date-picker
                  size="small"
                  v-model="item.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="confirmEnd(item, idx)"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                :loading="loading"
                :disabled="!item.beginTime || !item.endTime || loading"
                @click="confirmSet(item)"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
    </ul>
  </div>
</template>

<script>
import { getTimeSetting, saveTimeSetting } from "@/api/setting";
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      dataList: [],
      loading: false,
    };
  },
  created() {
    this.getSetting(1301);
  },
  methods: {
    // 获取
    getSetting(type) {
      getTimeSetting({ key: type }, this.prefixDeptCode).then((res) => {
        this.dataList = res;
      });
    },
    // 确认开始时间
    confirmStart(row, idx) {
      if (row.beginTime && row.endTime) {
        let start = new Date(row.beginTime).getTime();
        let end = new Date(row.endTime).getTime();
        if (end <= start) {
          this.$message.warning("结束时间不能小于等于开始时间");
          this.dataList[idx].beginTime = "";
        }
      }
    },
    // 确认结束时间
    confirmEnd(row, idx) {
      if (row.beginTime && row.endTime) {
        let start = new Date(row.beginTime).getTime();
        let end = new Date(row.endTime).getTime();
        if (end <= start) {
          this.$message.warning("结束时间不能小于等于开始时间");
          this.dataList[idx].endTime = "";
        }
      }
    },
    // 确认设置
    confirmSet(row) {
      this.loading = true;
      
      saveTimeSetting(
          {
            beginTime: row.beginTime,
            endTime: row.endTime,
            type: row.type,
            status: 0,
            setupId: row.setupId,
          },
          this.prefixDeptCode
      ).then((res) => {
        this.$message.success("操作成功");
      }).catch(err => {
        this.$message.error("保存失败：" + (err.message || "未知错误"));
      }).finally(() => (this.loading = false));
    },
  },
};
</script>

<style scoped lang="scss">
.setting-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;

  & > li {
    flex: 0 0 33.3333%;
    padding: 10px 10px;
    box-sizing: border-box;
  }
}
</style>