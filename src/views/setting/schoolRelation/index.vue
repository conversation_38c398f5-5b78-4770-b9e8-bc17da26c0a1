<!-- 学籍管理 -->
<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          >批量导入</el-button
        >
      </div>
<!--      <div class="sd-search">
        <el-input
          v-model.trim="search.keywords"
          placeholder="小学学校名称"
          size="small"
          clearable
          style="width: 240px"
        ></el-input>
        <el-input
            v-model.trim="search.keywords1"
            placeholder="初中学校名称"
            size="small"
            clearable
            style="width: 240px"
        ></el-input>
        <el-button
          type="primary"
          class="sd-btn-search"
          icon="el-icon-search"
          @click="searchSubmit"
          size="small"
        />
      </div>-->
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="小学学校名称"
        prop="primaryName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="初中学校名称"
        prop="juniorName"
      ></el-table-column>
      <el-table-column 
        align="center" 
        label="操作" 
        width="120"
      >
        <template slot-scope="scope">
          <el-link
            v-if="$store.getters.deptCode === '130284'"
            icon="el-icon-delete"
            type="danger"
            :underline="false"
            @click="handleDelete(scope.row)"
          >删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 删除选择对话框 -->
    <el-dialog
      title="选择删除类型"
      :visible.sync="modal.deleteSelect"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form label-width="120px">
        <el-form-item label="删除类型">
          <el-radio-group v-model="deleteType" @change="handleDeleteTypeChange">
            <el-radio :label="1">删除初中学校</el-radio>
            <el-radio :label="0">删除小学学校</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 当选择删除小学时显示下拉框 -->
        <el-form-item v-if="deleteType === 0" label="选择小学">
          <el-select v-model="selectedPrimary" placeholder="请选择要删除的小学" @change="handlePrimaryChange">
            <el-option
              v-for="item in primaryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <div class="flex-center">
        <el-button size="small" @click="switchModal('deleteSelect', false)">取消</el-button>
        <el-button size="small" type="danger" @click="confirmDelete">确认删除</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getGuanLianList, 
  importSchoolRelation, 
  deleteSchoolRelation,
  deleteJuniorGuanLian,
  deletePrimaryGuanLian
} from "@/api/setting";
import { getDepts } from "@/api/common";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: null,
        keywords1: null,
        schoolId: "",
        type: "1"
      },
      schoolList: [],
      dialogTitle: "批量导入",
      modal: {
        import: false,
        deleteSelect: false,
      },
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
      deleteType: 1,
      selectedPrimary: null,
      primaryList: [],
      currentRow: {},
      selectedPrimaryName: '',
    };
  },
  created() {
    this.getDepts();
  },
  computed: {
    switchGender() {
      return (val) => (val == "1" ? "男" : val == "2" ? "女" : "");
    }
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getGuanLianList(this.search).then((res) => {
        this.tableData = res;
        // 检查返回数据是否包含所需字段
        if (res.records && res.records.length > 0) {
          
          // 检查primaryIds字段是否存在
          if (!res.records[0].primaryIds && !res.records[0].primary_ids && 
              !res.records[0].primaryId && !res.records[0].primary_id) {
            console.warn('警告: 返回数据中没有找到小学ID字段');
          }
        }
        this.tableLoading = false;
      }).catch(err => {
        console.error('获取数据失败:', err);
        this.tableLoading = false;
      });
    },
    // 获取区县下的学校列表
    getDepts() {
      let params = {
        level: 3,
        period: "2",
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
        // 这里不需要设置primaryList，因为它会在handleDelete中根据当前行数据设置
      });
    },
    // 处理小学列表，将逗号分隔的小学名称转为选项
    processPrimaryList(row) {
      
      // 首先尝试使用传递过来的学校数据中的primaryList
      if (this.schoolList && this.schoolList.length > 0) {

        
        // 如果有小学名称，我们可以过滤只显示这些小学
        if (row && row.primaryName) {
          const primaryNames = row.primaryName.split(',').map(name => name.trim());

          
          // 根据名称从schoolList中过滤学校
          const filteredSchools = this.schoolList.filter(school => 
            primaryNames.some(name => school.name === name || school.deptName === name)
          );
          
          if (filteredSchools.length > 0) {

            return filteredSchools.map(school => ({
              label: school.name || school.deptName,
              value: school.id
            }));
          } else {

          }
        }
        
        // 如果没有名称匹配或过滤后为空，直接返回所有学校
        return this.schoolList.map(school => ({
          label: school.name || school.deptName,
          value: school.id
        }));
      }
      
      // 如果没有schoolList，尝试从行数据中获取
      if (!row || !row.primaryName) {

        return [];
      }
      
      // 获取小学名称数组
      const primaryNames = row.primaryName.split(',').map(name => name.trim());

      
      // 如果没有其他来源，至少返回名称列表
      return primaryNames.map(name => ({
        label: name,
        value: name // 使用名称作为临时ID
      }));
    },
    
    // 删除学校关系
    handleDelete(row) {

      this.currentRow = row;
      
      // 确保我们有juniorId - 这是表格中的学校ID
      if (!this.currentRow.juniorId && this.currentRow.id) {
        this.currentRow.juniorId = this.currentRow.id;
      }
      
      // 默认选择删除初中
      this.deleteType = 1;
      
      // 从getDepts获取的学校列表生成选项
      this.primaryList = this.processPrimaryList(row);
      

      // 检查是否有有效的小学ID可供选择
      if (this.primaryList.length === 0) {
        // 如果没有小学ID，只允许删除初中
        this.deleteType = 1;

      } else {
        // 默认选择第一个小学
        this.selectedPrimary = this.primaryList[0].value;
        this.selectedPrimaryName = this.primaryList[0].label;

      }
      
      // 打开删除选择对话框
      this.switchModal('deleteSelect', true);
    },
    
    // 监听小学选择变化
    handlePrimaryChange(value) {
      this.selectedPrimary = value;
      // 根据ID找到对应的名称
      const selectedItem = this.primaryList.find(item => item.value === value);
      this.selectedPrimaryName = selectedItem ? selectedItem.label : '';

    },
    
    // 确认删除
    confirmDelete() {
      // 检查是否选择了小学（如果是删除小学模式）
      if (this.deleteType === 0 && !this.selectedPrimary) {
        this.$message.warning('请选择要删除的小学');
        return;
      }
      
      // 获取初中ID
      let juniorId = this.currentRow.juniorId;
      if (!juniorId) {
        juniorId = this.currentRow.junior_id;
      }
      
      // 如果缺少必要的ID，显示错误消息并返回
      if (this.deleteType === 1 && !juniorId) {
        this.$message.error('缺少初中学校ID，无法执行删除操作');
        return;
      }
      
      if (this.deleteType === 0 && !this.selectedPrimary) {
        this.$message.error('请选择要删除的小学');
        return;
      }
      
      // 直接使用字符串格式的ID，不要转换为数值类型
      const primaryId = this.deleteType === 0 ? this.selectedPrimary : null;
      const juniorIdValue = this.deleteType === 1 ? juniorId : null;
      
      // 获取删除所需的信息
      const params = {
        key: this.deleteType, // 使用deleteType作为key，0表示小学，1表示初中
        deptId: this.deleteType === 1 
          ? juniorIdValue // 使用字符串格式的初中ID
          : primaryId // 使用字符串格式的小学ID
      };
      

      
      const confirmMessage = this.deleteType === 1
        ? `确认删除初中学校"${this.currentRow.juniorName}"吗？`
        : `确认删除小学学校"${this.selectedPrimaryName}"吗？`;
        
      this.$confirm(confirmMessage, "删除学校关系", {
        type: "error",
      }).then(() => {
        // 直接调用deleteSchoolRelation API

        
        deleteSchoolRelation(params).then(res => {
          this.$message.success("操作成功");
          this.switchModal('deleteSelect', false);
          this.getTableData();
        }).catch(err => {
          console.error('删除操作失败:', err);
          this.$message.error(`删除失败: ${err.message || '未知错误'}`);
        });
      }).catch(() => {

      });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        "/user-api/center/dept/downSchoolRelationTemplate",
        {},
        "xls",
        "学校关系导入模板.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importSchoolRelation(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
    // 处理删除类型变化
    handleDeleteTypeChange(value) {

      if (value === 0) { // 删除小学
        // 检查是否有小学列表数据
        if (this.primaryList.length === 0) {

          this.$message.warning('没有可选择的小学数据');
        } else if (!this.selectedPrimary) {
          // 如果没有选中的小学，默认选择第一个
          this.selectedPrimary = this.primaryList[0].value;
          this.selectedPrimaryName = this.primaryList[0].label;

        }
      }
    },
  },
};
</script>

<style scoped>
.warning-desc-text {
  color: #e6a23c;
}
.error-desc-text {
  color: #f56c6c;
}
</style>