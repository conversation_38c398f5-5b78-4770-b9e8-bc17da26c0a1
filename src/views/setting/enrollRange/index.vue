<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="add"
          >添加</el-button
        >
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          >批量导入</el-button
        >
      </div>
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-select
              size="small"
              v-model="search.period"
              placeholder="学段"
              @change="periodChange"
              clearable
            >
              <el-option
                v-for="item in periodOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="schoolId">
            <el-select
              v-model="search.schoolId"
              size="small"
              placeholder="所属学校"
              filterable
              clearable
            >
              <el-option
                v-for="item in schoolList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="搜索小区/村庄名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      style="width: 100%"
      v-loading="tableLoading"
    >
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="所属学校"
        prop="schoolName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="区域"
        prop="rangeName"
      ></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            @click="edit(row)"
            >编辑招生范围</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item label="选择学段" prop="period">
          <el-select
            v-model="form.period"
            @change="periodChange1"
            style="width: 220px"
            :disabled="isFive"
          >
            <el-option
              v-for="item in periodOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="schoolId" label="所属学校">
          <el-select v-model="form.schoolId" size="small" style="width: 220px">
            <el-option
              v-for="item in schoolList1"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="rangeName" label="招生范围">
          <el-input
            size="small"
            v-model.trim="form.rangeName"
            placeholder="请输入招生范围"
            style="width: 220px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSchoolRangeList,
  createSchoolRange,
  importSchoolRange,
} from "@/api/setting";
import { getDepts } from "@/api/common";
import { schoolTypeOptions } from "@/utils/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      isFive:this.$store.getters.userInfo.isFive,
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: "",
        period: "",
        schoolId: "",
        countyId: this.$store.getters.deptId,
        type: 1, // 1区县级别 2学校级别
      },
      form: {
        id: "",
        period: "",
        schoolId: "",
        rangeName: "",
      },
      rules: {
        period: [
          { required: true, message: "请选择学段", trigger: "change" },
        ],
        schoolId: [
          { required: true, message: "请选择学校", trigger: "change" },
        ],
        rangeName: [
          { required: true, message: "请输入招生范围", trigger: "blur" },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
        import: false,
      },
      schoolList: [],
      schoolList1: [],
      periodOptions: schoolTypeOptions,
      downloadLoading: false,
      fileList: [],
      errorMessages: [],
    };
  },
  created() {
    if(this.isFive){
      this.form.period='2'
    this.getDepts()
    this.getDepts1()
    }
    console.log(this.$store.getters.userInfo.isFive,"this.$store")
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getSchoolRangeList(this.search)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 根据学段获取学校
    periodChange() {
      this.getDepts();
      this.search.schoolId = "";
    },
    // 获取学校
    getDepts() {
      let params = {
        level: 3,
        period: this.search.period,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 添加中根据学段获取学校
    periodChange1() {
      this.getDepts1();
      this.form.schoolId = "";
    },
    // 添加中获取学校
    getDepts1() {
      let params = {
        level: 3,
        period: this.form.period,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList1 = res;
      });
    },
    // 添加
    add() {
      this.dialogTitle = "招生范围";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    // 编辑
    edit(row) {
      this.$router.push({
        path: "/setting/enrollRangeDetail",
        query: { schoolId: row.schoolId, schoolName: row.schoolName },
      });
    },
    // 确认更新
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            createSchoolRange(this.form).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          }
        }
      });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.downloadLoading = true;
      this.$download(
        "/user-api/center/schoolRang/downTemplate",
        {},
        "xls",
        "学校招生范围导入模版.xls"
      ).then((res) => {
        this.$message
          .success("下载成功")
          .finally(() => (this.downloadLoading = false));
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importSchoolRange(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
