<template>
  <div>
    <el-tabs v-model="curTab" @tab-click="handleTabClick" type="border-card">
      <el-tab-pane label="乡镇" name="county">
        <el-table
          :data="tableData.county"
          border
          stripe
          v-loading="tableLoading"
        >
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="60"
            fixed="left"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="field"
            label="学段"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="signUpSort"
            label="报名类别"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="detail"
            label="报名类别详情"
          ></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <el-link
                @click="edit(row)"
                icon="el-icon-edit"
                type="warning"
                :underline="false"
                >编辑
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane :label="cityName" name="city">
        <el-table :data="tableData.city" border stripe v-loading="tableLoading">
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="60"
            fixed="left"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="field"
            label="学段"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="signUpSort"
            label="报名类别"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="detail"
            label="报名类别详情"
          ></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <el-link
                @click="edit(row)"
                icon="el-icon-edit"
                type="warning"
                :underline="false"
                >编辑
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane v-if="this.$store.getters.deptCode === '130209'" label="新城区" name="newCity">
        <el-table :data="tableData.newCity" border stripe v-loading="tableLoading">
          <el-table-column
              align="center"
              type="index"
              label="序号"
              width="60"
              fixed="left"
          >
          </el-table-column>
          <el-table-column
              align="center"
              prop="field"
              label="学段"
          ></el-table-column>
          <el-table-column
              align="center"
              prop="signUpSort"
              label="报名类别"
          ></el-table-column>
          <el-table-column
              align="center"
              prop="detail"
              label="报名类别详情"
          ></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <el-link
                  @click="edit(row)"
                  icon="el-icon-edit"
                  type="warning"
                  :underline="false"
              >编辑
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane v-if="this.$store.getters.deptCode === '130209'" label="工业区" name="industrial">
        <el-table :data="tableData.industrial" border stripe v-loading="tableLoading">
          <el-table-column
              align="center"
              type="index"
              label="序号"
              width="60"
              fixed="left"
          >
          </el-table-column>
          <el-table-column
              align="center"
              prop="field"
              label="学段"
          ></el-table-column>
          <el-table-column
              align="center"
              prop="signUpSort"
              label="报名类别"
          ></el-table-column>
          <el-table-column
              align="center"
              prop="detail"
              label="报名类别详情"
          ></el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="{ row }">
              <el-link
                  @click="edit(row)"
                  icon="el-icon-edit"
                  type="warning"
                  :underline="false"
              >编辑
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>


    </el-tabs>

    <!-- 编辑 -->
    <el-dialog
      title="编辑"
      :visible.sync="modal.addOrEdit"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        label-position="right"
        label-width="140px"
      >
        <el-form-item prop="title" label="报名类别：">
          <span>{{ categoryTitle }}</span>
        </el-form-item>
        <el-form-item prop="detail" label="报名类别详情：">
          <el-input
            type="textarea"
            :rows="8"
            v-model.trim="form.detail"
            placeholder="请输入报名类别详情,至少5个字符"
            style="width: 400px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import {
  signUpSortDetailSetting,
  signUpSortDetailSettingUpdate,
} from "@/api/setting";

export default {
  name: "categoryDetail",
  mixins: [ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      curTab: "county",
      cityName: this.$store.getters.deptCode === '130209'? '主城区':'城区',
      tableData: {
        city: [],
        county: [],
        industrial:[],
        newCity:[]
      },
      modal: {
        addOrEdit: false,
      },
      tableLoading: false,
      form: {
        id: "",
        detail: "",
      },
      rules: {
        detail: [
          {
            required: true,
            message: "请输入类别详情",
            trigger: "blur",
          },
          {
            min: 5,
            message: "请至少输入5个以上字符",
            trigger: "blur",
          },
        ],
      },
      categoryTitle: "",
    };
  },
  created() {
    this.getTableDataCounty();
    // this.getTableDataCity();
    // // //新城区
    // this.getTableDataNewCity();
    // // //工业区
    // this.getTableDataIndustrial();
  },
  methods: {
    handleTabClick(tab) {
      console.log('Tab clicked:', tab.name);
      // You can perform actions based on which tab was clicked
      switch(tab.name) {
        case 'county':
          this.getTableDataCounty();
          break;
        case 'city':
          this.getTableDataCity();
          break;
        case 'newCity':
          this.getTableDataNewCity();
          break;
        case 'industrial':
          this.getTableDataIndustrial();
          break;
      }
    },


    // 乡镇列表
    getTableDataCounty() {
      this.tableLoading = true;
      signUpSortDetailSetting({ key: 2 }, this.prefixDeptCode)
        .then((res) => {
          this.tableData.county = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    // 城区列表
    getTableDataCity() {
      this.tableLoading = true;
      signUpSortDetailSetting({ key: 3 }, this.prefixDeptCode)
        .then((res) => {
          this.tableData.city = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 新城区
    getTableDataNewCity() {
      this.tableLoading = true;
      signUpSortDetailSetting({ key: 116 }, this.prefixDeptCode)
          .then((res) => {
            this.tableData.newCity = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 工业区
    getTableDataIndustrial() {
      this.tableLoading = true;
      signUpSortDetailSetting({ key: 117 }, this.prefixDeptCode)
          .then((res) => {
            this.tableData.industrial = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 编辑
    edit(row) {
      console.log(row);
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        var curCategoryTitle = "";
        if(this.curTab ==='county'){
          curCategoryTitle = "乡镇";
        }else if(this.curTab === 'city'){
          curCategoryTitle = this.$store.getters.deptCode === '130209'? '主城区':'城区';
        }else if(this.curTab === 'industrial'){
          curCategoryTitle = "工业区";
        }else if(this.curTab === 'newCity'){
          curCategoryTitle = "新业区";
        }


        this.$refs.form.resetFields();
        this.form.id = row.setupId;
        this.form.detail = row.detail;
        this.categoryTitle =
        curCategoryTitle +
          " - " +
          row.field +
          " - " +
          row.signUpSort;
      });
    },
    // 更新
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          signUpSortDetailSettingUpdate(this.form, this.prefixDeptCode).then(
            () => {
              this.switchModal("addOrEdit", false);
              this.$message.success("操作成功");
              if (this.curTab === "county") {
                this.getTableDataCounty();
              }else if (this.curTab === 'city'){
                this.getTableDataCity();
              }else if (this.curTab === 'newCity'){
                this.getTableDataNewCity();
              }else if (this.curTab === 'industrial'){
                this.getTableDataIndustrial();
              }
            }
          );
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
