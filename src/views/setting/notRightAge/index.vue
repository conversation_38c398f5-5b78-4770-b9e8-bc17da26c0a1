<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button size="small" type="primary" @click="add" icon="el-icon-plus"
          >添加</el-button
        >
      </div>
      <div class="sd-search">
        <el-input
          v-model.trim="search.keywords"
          placeholder="请输入身份证号"
          size="small"
          clearable
          style="width: 240px"
        ></el-input>
        <el-button
          type="primary"
          class="sd-btn-search"
          icon="el-icon-search"
          @click="searchSubmit"
          size="small"
        />
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="姓名"
        prop="name"
      ></el-table-column>
      <el-table-column
        align="center"
        label="身份证号"
        prop="idCard"
      ></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{ row }">
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="delStu(row)"
            :underline="false"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 添加 -->
    <el-dialog
      title="添加信息"
      :visible.sync="modal.addOrEdit"
      center
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="auto"
        @keydown.enter="save"
      >
        <el-row>
          <el-col :span="16" :offset="4">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model.trim="form.name"
                placeholder="请输入真实姓名"
                clearable
                show-word-limit
                size="small"
              />
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input
                v-model.trim="form.idCard"
                placeholder="请输入身份证号"
                clearable
                maxlength="18"
                show-word-limit
                size="small"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="flex-center" style="margin-top: 40px">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  nonProperAgeStuList,
	addNonProperAgeStu,
	delNonProperAgeStu
} from "@/api/notRightAge";
import { idCardValidator } from "@/utils/validator";
export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      isFive: this.$store.getters.userInfo.isFive,
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: '',
      },
      modal: {
        addOrEdit: false,
      },
      form: {
        name: '',
        idCard: '',
      },
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        idCard: [
          { required: true, validator: idCardValidator, trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true
			nonProperAgeStuList(this.search, this.prefixDeptCode).then(res => {
				this.tableData = res
				this.tableLoading = false
			})
    },
    // 添加
    add() {
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs["form"].resetFields();
      });
    },
    // 删除
    delStu(row) {
      this.$confirm(`确定删除【${row.name}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
				delNonProperAgeStu({ key: row.id }, this.prefixDeptCode).then(() => {
					this.$message.success("删除成功")
					this.getTableData()
				});
      });
    },
    // 添加 修改 - 提交
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
					addNonProperAgeStu(this.form, this.prefixDeptCode).then(() => {
						this.$message.success("添加成功");
						this.switchModal("addOrEdit", false);
						this.getTableData();
					});
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
