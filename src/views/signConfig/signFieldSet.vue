<!-- 报名类别设置 -->
<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          type="primary"
          plain
          icon="el-icon-back"
          size="small"
          @click="back"
          >返回</el-button
        >
      </div>
    </div>
    <div class="field-set">
      <div class="field-set-left">
        <div class="field-set-title">功能大纲</div>
        <el-tree
          :data="treeData"
          node-key="id"
          ref="tree"
          highlight-current
          default-expand-all
          :props="defaultProps"
          :indent="30"
          @node-click="treeNodeClick"
          v-loading="treeLoading"
        >
        </el-tree>
      </div>
      <div class="field-set-right">
        <div class="county-title">{{ county }}</div>
        <div class="field-set-title">字段配置 - {{ nodePath }}</div>
        <el-table
          :data="tableData.records"
          border
          stripe
          v-loading="tableLoading"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column
            prop="fieldName"
            label="配置项"
            align="center"
          ></el-table-column>
          <el-table-column prop="isShow" label="展示项" align="center">
            <template slot-scope="{ row }">
              <el-radio-group v-model="row.isShow" @input="isShowChange(row)">
                <el-radio :label="1">显示</el-radio>
                <el-radio :label="0">不显示</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="300"
            fixed="right"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-link
                type="success"
                @click="handleFieldSet(row.configId)"
                :underline="false"
                icon="el-icon-s-operation"
                :disabled="!row.isShow"
                >配置
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import {
  getSystemSetupTreeFunction,
  getRootConfigIsShow,
  setRootConfigIsShow,
} from "@/api/signConfig";
export default {
  name: "signFieldSet",
  mixins: [TableMixin],
  components: {},
  data() {
    return {
      treeData: [],
      treeSelectedNodeId: "",
      treeLoading: false,
      defaultProps: {
        children: "children",
        label: "name",
      },
      nodePath: "",
      prefixDeptCode: "",
      county: "",
      setUpSaveIds: "",
    };
  },
  created() {
    this.prefixDeptCode = this.$route.query.code;
    this.county = this.$route.query.county;
    this.setUpSaveIds = this.$route.query.setUpSaveIds;
  },
  methods: {
    // 获取列表
    async getTableData() {
      let id = await this.getSystemSetupTreeFunction();
      if (id) {
        let nodePathObj = this.$refs.tree.getNode(id);
        let nodePathArray = [
          `${nodePathObj.parent.parent.parent.label}`,
          `${nodePathObj.parent.parent.label}`,
          `${nodePathObj.parent.label}`,
          `${nodePathObj.label}`,
        ];
        this.nodePath = nodePathArray.join(" / ");
        this.getRootConfigIsShow(id);
      }
    },
    // 获取功能大纲树数据
    async getSystemSetupTreeFunction() {
      this.treeLoading = true;
      this.treeData = await getSystemSetupTreeFunction({}, this.prefixDeptCode);
      if (this.treeData.length > 0) {
        // 配置字段页面返回时，选中地址栏的报名类别
        if (this.setUpSaveIds) {
          this.treeSelectedNodeId = this.setUpSaveIds;
        }
        // 默认选中第一个报名类别
        else {
          this.treeSelectedNodeId =
            this.treeData[0].children[0].children[0].children[0].id;
        }
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.treeSelectedNodeId);
          this.treeLoading = false;
        });
      }
      return this.treeSelectedNodeId || "";
    },
    // 查询列表
    getRootConfigIsShow(id) {
      this.tableLoading = true;
      getRootConfigIsShow({ key: id }, this.prefixDeptCode)
        .then((res) => {
          this.tableData.records = res;
          if (this.tableData.records.length > 0) {
            this.tableData.records[0].fieldName = "基本信息";
            this.tableData.records[1].fieldName = "房产信息";
            this.tableData.records[2].fieldName = "图片管理";
            this.tableData.records[3].fieldName = "优抚类型";
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.tableLoading = false;
          }, 150);
        });
    },
    // 选择树节点
    treeNodeClick(obj) {
      // 最末级level = 4
      let nodePathObj = this.$refs.tree.getNode(obj.id);
      if (nodePathObj.level == 4) {
        this.treeSelectedNodeId = obj.id;
        this.getRootConfigIsShow(this.treeSelectedNodeId);
        // 更新报名类别路径名称
        let nodePathArray = [
          `${nodePathObj.parent.parent.parent.label}`,
          `${nodePathObj.parent.parent.label}`,
          `${nodePathObj.parent.label}`,
          `${nodePathObj.label}`,
        ];
        this.nodePath = nodePathArray.join(" / ");
        // 地址栏更新报名类别参数setUpSaveIds
        this.$router.push({
          query: {
            ...this.$route.query,
            setUpSaveIds: this.treeSelectedNodeId,
          },
        });
      }
    },
    // 是否显示操作
    isShowChange(row) {
      let params = {
        setUpSaveIds: this.treeSelectedNodeId,
        configId: row.configId,
        key: row.isShow,
      };
      setRootConfigIsShow(params, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getRootConfigIsShow(this.treeSelectedNodeId);
      });
    },
    // 配置字段
    handleFieldSet(configId) {
      this.$router.push({
        path: "/signConfig/signFieldSetDetail",
        query: {
          setUpSaveIds: this.treeSelectedNodeId,
          configId,
          nodePath: this.nodePath,
          code: this.prefixDeptCode,
        },
      });
    },
    // 返回
    back() {
      this.$router.push({
        path: "/signConfig/index",
        query: {
          code: this.prefixDeptCode
        } 
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.county-title {
  font-size: 18px;
  font-weight: bold;
  position: absolute;
  left: 50%;
  top: -35px;
  transform: translateX(-50%);
}
.field-set {
  display: flex;
  justify-content: space-between;
  .field-set-left {
    width: 200px;
    border: 1px solid #a2a4a7;
    padding: 10px;
    height: calc(100vh - var(--header-height) - 95px);
    overflow-y: scroll;
    margin-right: 15px;
    border-radius: 3px;
  }
  .field-set-right {
    width: calc(100% - 240px);
    padding: 10px;
    border: 1px solid #a2a4a7;
    height: calc(100vh - var(--header-height) - 95px);
    border-radius: 3px;
    position: relative;
  }
  .field-set-title {
    padding-bottom: 15px;
  }
}
</style>