<template>
    <div class="container flexd center" v-loading="loadingShow">
        <div class="inners">
            <h1>初中电脑派位</h1>
            <div class="flexd selectBox">
                <div class="center">
                    <div class="cubes" :class="{'Acts':step1,finished:stepLevel>0}">
                        <div class="imgBox"></div>
                        <div>{{stepLevel==0?'第一志愿电脑派位':'第一志愿派位结束'}}</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button :disabled="!step1" @click="paiwei(1)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>
                </div>
                <div class="flexd center ar"><i class="el-icon-d-arrow-right"></i></div>
                <div class="center">
                    <div class="cubes" :class="{Acts:step2,finished:stepLevel>1}">
                        <div class="imgBox"></div>
                        <div>{{stepLevel>1?'第二志愿派位结束':'第二志愿电脑派位'}}</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button :disabled="!step2" @click="paiwei(2)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>

                </div>
                <div class="flexd center ar"><i class="el-icon-d-arrow-right"></i></div>
                <div class="center">
                    <div class="cubes" :class="{Acts:step3,finished:stepLevel>2}">
                        <div class="imgBox"></div>
                        <div>{{stepLevel>2?'第三志愿派位结束':'第三志愿电脑派位'}}</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button :disabled="!step3" @click="paiwei(3)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>
                </div>
            </div>


            <div class="flexd selectBox">
                <div class="center">
                    <div class="cubes" :class="{'Acts':step4,finished:stepLevel>0}">
                        <div class="imgBox"></div>
                        <div>{{stepLevel>3?'第四志愿派位结束':'第四志愿电脑派位'}}</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button :disabled="!step4" @click="paiwei(4)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>
                </div>
                <div class="flexd center ar"><i class="el-icon-d-arrow-right"></i></div>
                <div class="center">
                    <div class="cubes" :class="{Acts:step5,finished:stepLevel>1}">
                        <div class="imgBox"></div>
                        <div>{{stepLevel>4?'第五志愿派位结束':'第五志愿电脑派位'}}</div>
                    </div>
                    <div class="flexd center btns">
                        <el-button :disabled="!step5" @click="paiwei(5)" type="danger" class="paiweiBtn">进行电脑派位
                        </el-button>
                    </div>

                </div>
                <!--<div class="flexd center ar"><i class="el-icon-d-arrow-right"></i></div>-->
                <!--<div class="center">-->
                    <!--<div class="cubes" :class="{Acts:step6,finished:stepLevel>2}">-->
                        <!--<div class="imgBox"></div>-->
                        <!--<div>{{stepLevel>5?'第六志愿派位结束':'第六志愿电脑派位'}}</div>-->
                    <!--</div>-->
                    <!--<div class="flexd center btns">-->
                        <!--<el-button :disabled="!step6" @click="paiwei(6)" type="danger" class="paiweiBtn">进行电脑派位-->
                        <!--</el-button>-->
                    <!--</div>-->
                <!--</div>-->
            </div>
        </div>
    </div>
</template>
<script>
    // import {
    //     assignmentEnrollTimeIsEndApi,
    //     assignmentGetPaiweiStatusApi
    // } from "../api/api";
    import {zhiYuanZhuangTai} from "@/api/quXianPW";
    export default {
        data() {
            return {
              prefixDeptCode: this.$store.getters.deptCode,
                step1: false,
                step2: false,
                step3: false,
                step4: false,
                step5: false,
                step6: false,
                stepLevel: 0,
                loadingShow: true,
                isEnd: "",
                type: 'local'//outland
            }
        },
        async created() {
            let type = this.$route.query.type;
            if (type) {
                this.type = type
            }
            await this.enrollTimeLoad();
            await this.setPeiWeiStatus();

        },
        methods: {
            enrollTimeLoad() {
                // assignmentEnrollTimeIsEndApi().then(res => {
                //     if (res && res.code == "200") {
                //         this.isEnd = res.result.timeEnd;
                //     } else {
                //         return false;
                //     }
                // });
            },
            setPeiWeiStatus() {
              // this.loadingShow=false
              // this.step1=true
                 //this.loadingShow = true;
              zhiYuanZhuangTai({},this.prefixDeptCode).then(res => {
                    console.log(res);
                    // if (res && res.code == "200") {
                        this.loadingShow = false;
                        this.step1 = res.first;
                        this.step2 = !this.step1 && res.second;
                        this.step3 = !this.step1 &&!this.step2 && res.third;
                        this.step4 = !this.step1 &&!this.step2 && !this.step3 && res.fourth;
                        this.step5 = !this.step1 &&!this.step2 && !this.step3 && !this.step4 && res.fifth;

                        if (this.step1) {
                            this.stepLevel = 0
                        } else if (this.step2) {
                            this.stepLevel = 1
                        } else if (this.step3) {
                            this.stepLevel = 2
                        } else if (this.step4) {
                            this.stepLevel = 3
                        } else if (this.step5) {
                            this.stepLevel = 4
                        } else if (this.step6) {
                            this.stepLevel = 5
                        }else{
                            this.stepLevel = 6
                        }
                    // } else {
                    //     this.$message.error(res.msg);
                    //     return false;
                    // }
                });
            },
            // 接口 获取 step1 ，2 ，3 的开启状态字段。用于设置对应值
            // 复制一份用于外地派位
            paiwei(e) {
                this.$router.push('/dispatch/assignmentSchoolList?type=' + this.type + '&step=' + e)
            }
        }
    }
</script>
<style>
@import "../../../../public/index.css";
</style>
<style lang="less" scoped>

    .container {
      //.flexd {
      //  display: flex;
      //  align-items: center;
      //}
        .selectBox {
            padding-top: 36px;
            .btns {
                margin-top: 24px;
            }
            .ar {
                font-size: 45px;
                padding: 24px;
                margin-bottom: 72px;
                i {
                    font-weight: bold;
                }
            }
            .cubes {
                border-radius: 8px;
                padding: 12px 18px;
                background: rgb(0, 255, 255);

                .imgBox {
                    width: 180px;
                    height: 180px;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: cover;
                    margin-bottom: 12px;
                    background-image: url('../../../assets/img/buok.png');

                }
                &.Acts {
                    .imgBox {
                        background-image: url('../../../assets/img/ok.png');
                    }

                }
                &.finished {
                    .paiweiBtn {
                        background: rgba(0, 0, 0, .8);
                        border: none;
                    }

                }
            }
        }
    }
</style>
