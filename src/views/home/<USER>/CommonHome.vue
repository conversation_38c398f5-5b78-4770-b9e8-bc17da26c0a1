<template>
  <div>
    <ul class="total-num">
      <li v-if="$store.getters.deptCode !== '130205'">
        <h2>乡镇小学报名数</h2>
        <span>{{ tableData.villagesPrimarySchoolNum }}人</span>
      </li>
      <li v-if="$store.getters.deptCode !== '130205'">
        <h2>乡镇初中报名数</h2>
        <span>{{ tableData.villagesJuniorSchoolNum }}人</span>
      </li>
      <li>
        <h2 v-if="$store.getters.deptCode === '130205'">公办小学报名数</h2>
        <h2 v-else-if="$store.getters.deptCode === '130209'">主城区小学报名数</h2>
        <h2 v-else>城区小学报名数</h2>
        <span>{{ tableData.urbanPrimarySchoolNum }}人</span>
      </li>
      <li>
        <h2 v-if="$store.getters.deptCode === '130205'">公办初中报名数</h2>
        <h2 v-else-if="$store.getters.deptCode === '130209'">主城区初中报名数</h2>
        <h2 v-else>城区初中报名数</h2>
        <span>{{ tableData.urbanJuniorSchoolNum }}人</span>
      </li>
      <li v-if="$store.getters.deptCode === '130299' || $store.getters.deptCode === '130205'|| $store.getters.deptCode === '130225' || $store.getters.deptCode === '130284'">
        <h2>民办小学报名数</h2>
        <span>{{ tableData.privateePrimarySchoolNum }}人</span>
      </li>
      <li v-if="$store.getters.deptCode === '130299' || $store.getters.deptCode === '130205'|| $store.getters.deptCode === '130225' || $store.getters.deptCode === '130284'">
        <h2>民办初中报名数</h2>
        <span>{{ tableData.privateeJuniorSchoolNum }}人</span>
      </li>
    </ul>

    <ul class="total-num" v-if="$store.getters.deptCode === '130209'">
      <li>
        <h2>新城区小学报名数</h2>
        <span>{{ tableData.newCityPrimarySchoolNum }}人</span>
      </li>
      <li>
        <h2>新城区初中报名数</h2>
        <span>{{ tableData.newCityJuniorSchoolNum }}人</span>
      </li>
      <li>
        <h2>工业区小学报名数</h2>
        <span>{{ tableData.industrialPrimarySchoolNum }}人</span>
      </li>
      <li>
        <h2>工业区初中报名数</h2>
        <span>{{ tableData.industrialJuniorSchoolNum }}人</span>
      </li>
    </ul>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-button
            size="small"
            type="primary"
            icon="el-icon-view"
            @click="detail"
        >查看详情
        </el-button
        >
      </div>
      <div class="sd-options">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-select
                size="small"
                v-model="search.nature"
                placeholder="选择学校性质"
                clearable
            >
              <el-option label="乡镇" :value="1" v-if="$store.getters.deptCode !== '130205'"></el-option>
              <el-option :label="$store.getters.deptCode === '130205' ? '公办' : ($store.getters.deptCode === '130209' ? '主城区' : '城区')" :value="2"></el-option>
              <el-option label="民办" :value="5" v-if="$store.getters.deptCode === '130205'"></el-option>
              <el-option label="新城区" :value="3" v-if="$store.getters.deptCode === '130209'"></el-option>
              <el-option label="工业区" :value="4" v-if="$store.getters.deptCode === '130209'"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.period"
                placeholder="选择学段"
                clearable
            >
              <el-option label="幼儿园" :value="1"  v-if="$store.getters.deptCode != '130205' && this.$store.getters.deptCode != '130204'"></el-option>
              <el-option label="小学" :value="2"></el-option>
              <el-option label="初中" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table
        :data="tableData.pageInfo?.records"
        border
        stripe
        v-loading="tableLoading"
    >
      <el-table-column
          align="center"
          label="序号"
          type="index"
          width="60"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学校名称"
          prop="schoolName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学校性质"
          prop="natureName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学段"
          prop="periodName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="计划招生数"
          prop="planNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="报名人数"
          prop="registrationNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="审核通过数(以最终教育局数为准)"
          prop="auditPassNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="审核驳回数(以最终教育局数为准)"
          prop="auditRefuseNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="调剂数"
          prop="adjustNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学校报到数"
          prop="schoolReportNum"
      ></el-table-column>
    </el-table>
    <div class="page-container" v-if="pageTotal > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="pageTotal"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import {homeStatistics, sheXianHomeStatistics} from "@/api/statistics";

export default {
  name: 'CommonHome',
  mixins: [TableMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      search: {
        keywords: "",
        nature: "",
        period: "",
      },
    };
  },
  computed: {
    pageTotal() {
      console.log(this.prefixDeptCode)
      return this.tableData.pageInfo && this.tableData.pageInfo.total
          ? Number(this.tableData.pageInfo.total)
          : 0;
    },
  },
  created() {
    this.search.keywords = "";
    this.search.nature = "";
    this.search.period = "";
    console.log(this.search);
  },
  methods: {
    getTableData() {
      this.tableLoading = true;
      if (this.prefixDeptCode == '130426') {
        sheXianHomeStatistics(this.search, this.prefixDeptCode).then((res) => {
          this.tableData = res;
        })
            .finally(() => {
              this.tableLoading = false;
            });

      } else {
        homeStatistics(this.search, this.prefixDeptCode)
            .then((res) => {
              this.tableData = res;
            })
            .finally(() => {
              this.tableLoading = false;
            });
      }

    },
    // 查看详情 - 跳转到数据统计，小学统计
    detail() {
      if (this.prefixDeptCode == '130426') {
        this.$router.push("/dataStatistics/primary_shexian");
      } else {
        this.$router.push("/dataStatistics/primary");
      }

    },
  },
};
</script>

<style lang='scss' scoped>
.total-num {
  display: flex;
  border: 1px solid #ccc;
  border-radius: 4px;
  list-style: none;
  margin-bottom: 30px;

  & > li {
    height: 200px;
    flex: 0 0 15%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    & > span {
      padding-top: 40px;
    }
  }
}
</style>
