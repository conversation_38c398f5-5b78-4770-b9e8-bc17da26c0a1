import VueRouter from 'vue-router'
import Vue from 'vue'
import Layout from '@/layouts/index.vue'

Vue.use(VueRouter)
// 峰峰矿区
// const fengfeng = ['130406']
// 临漳
// const linzhang = ['130423']
// 标准版
//    馆陶     武安      广平    大名     磁县     魏县    峰峰矿区   临漳     曲周     成安
// ['130433','130481','130432','130425','130427','130434','130406','130423','130435','130424']
// 非标准版
// 邱县 ['130430']

const routes = [
    {
        path: '/',
        component: Layout,
        redirect: '/index',
        meta: {
            hidden: false,
            title: '首页',
            roles: ['SUPER_ADMIN', 'COUNTY_ADMIN'],
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/home'),
                meta: {
                    hidden: false,
                    title: '首页',
                    roles: ['SUPER_ADMIN', 'COUNTY_ADMIN']
                }
            }
        ],
    },
    {
        path: '/delaySchool',
        component: Layout,
        redirect: '/delaySchool/index',
        meta: {
            hidden: false,
            title: '小学延缓入学申请',
            roles: ['COUNTY_ADMIN',"SCHOOL"],
            depts: ['130284']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/delaySchool/index.vue'),
                meta: {
                    hidden: false,
                    title: '小学延缓入学申请',
                    roles: ['COUNTY_ADMIN',"SCHOOL"],
                    depts: ['130284']
                }
            }
        ],
    },
    {
        path: '/delaySchool',
        component: Layout,
        redirect: '/delaySchool/index',
        meta: {
            hidden: false,
            title: '小学延缓入学申请',
            roles: ['COUNTY_ADMIN'],
            depts: ['130225']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/delaySchool/index.vue'),
                meta: {
                    hidden: false,
                    title: '小学延缓入学申请',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130225']
                }
            }
        ],
    },
    {
        path: '/outsideSchool',
        component: Layout,
        redirect: '/outsideSchool/index',
        meta: {
            hidden: false,
            title: '外地上学申请管理',
            roles: ['COUNTY_ADMIN','SCHOOL'],
            depts: ['130284']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/outsideSchool/index.vue'),
                meta: {
                    hidden: false,
                    title: '外地上学申请管理',
                    roles: ['COUNTY_ADMIN','SCHOOL'],
                    depts: ['130284']
                }
            }
        ],
    },
    {
        path: '/sysConfig',
        component: Layout,
        meta: {
            hidden: false,
            title: '系统设置',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'clearSignData',
                component: () => import('@/views/sysConfig/clearSignData'),
                meta: {
                    hidden: false,
                    title: '公办清除学生报名数据',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'privateClearSignData',
                component: () => import('@/views/sysConfig/privateClearSignData'),
                meta: {
                    hidden: false,
                    title: '民办清除学生报名数据',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'sysLog',
                component: () => import('@/views/sysConfig/sysLog'),
                meta: {
                    hidden: false,
                    title: '操作日志',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'sysAccounts',
                component: () => import('@/views/sysConfig/user/user.vue'),
                meta: {
                    hidden: false,
                    title: '账号管理',
                    roles: ['SUPER_ADMIN']
                }
            }
        ],
    },
    {
        path: '/signConfig',
        component: Layout,
        redirect: '/signConfig/index',
        meta: {
            hidden: false,
            title: '报名设置',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/signConfig/index'),
                meta: {
                    hidden: false,
                    title: '报名设置',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'signFieldSet',
                name: 'signFieldSet',
                component: () => import('@/views/signConfig/signFieldSet.vue'),
                meta: {
                    hidden: true,
                    title: '报名字段设置',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'signFieldSetDetail',
                name: 'signFieldSetDetail',
                component: () => import('@/views/signConfig/signFieldSetDetail'),
                meta: {
                    hidden: true,
                    title: '报名字段设置',
                    roles: ['SUPER_ADMIN']
                }
            }
        ],
    },
    {
        path: '/signFields',
        component: Layout,
        meta: {
            hidden: false,
            title: '报名填写字段',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'baseInfo',
                component: () => import('@/views/signConfig/baseInfo'),
                meta: {
                    hidden: false,
                    title: '基础信息',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'houseInfo',
                component: () => import('@/views/signConfig/houseInfo'),
                meta: {
                    hidden: false,
                    title: '房产信息',
                    roles: ['SUPER_ADMIN']
                }
            },
            {
                path: 'imageInfo',
                component: () => import('@/views/signConfig/imageInfo'),
                meta: {
                    hidden: false,
                    title: '照片管理',
                    roles: ['SUPER_ADMIN']
                }
            }
        ],
    },
    {
        path: '/signEntrance',
        component: Layout,
        redirect: '/signEntrance/index',
        meta: {
            hidden: false,
            title: '报名入口管理',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/signConfig/signEntrance'),
                meta: {
                    hidden: false,
                    title: '报名入口管理',
                    roles: ['SUPER_ADMIN']
                }
            },
        ],
    },
    {
        path: '/signExportSet',
        component: Layout,
        redirect: '/signExportSet/index',
        meta: {
            hidden: false,
            title: '导出报名表格配置',
            roles: ['SUPER_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/signExportSet'),
                meta: {
                    hidden: false,
                    title: '导出报名表格配置',
                    roles: ['SUPER_ADMIN']
                }
            },
        ],
    },
    {
        path: '/enrollment',
        component: Layout,
        meta: {
            hidden: false,
            title: '招生',
            roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL','TEACHER'],
            isFive: false
        },
        children: [
            {
                path: 'primary',
                component: () => import('@/views/enrollment/primary'),
                meta: {
                    hidden: false,
                    title: '小学报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus:3,
                    depts: ['130207', '130274', '130209', '130204', '130272', '130403', '130225', '130284', '130224', '130271', '130285','130202','130299','130205']
                }
            },
            {
                path: 'primaryFn',
                component: () => import('@/views/enrollment/primaryFn'),
                meta: {
                    hidden: false,
                    title: '毕业生审核',
                    roles: [ 'SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus:3,
                    depts: ['130207']
                }
            },
            {
                path: 'primaryFn',
                component: () => import('@/views/enrollment/primaryFn'),
                meta: {
                    hidden: false,
                    title: '初中报名列表',
                    roles: [ 'COUNTY_ADMIN', 'AUDITOR'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus:3,
                    depts: ['130207']
                }
            },
            {
                path: 'primaryQW',
                component: () => import('@/views/enrollment/primaryQW'),
                meta: {
                    hidden: false,
                    title: '区外就读报名列表',
                    roles: [ 'COUNTY_ADMIN', 'AUDITOR','SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus:3,
                    depts: ['130207']
                }
            },
            {
                path: 'juniorQW',
                component: () => import('@/views/enrollment/juniorQW'),
                meta: {
                    hidden: false,
                    title: '区外就读报名列表',
                    roles: [ 'COUNTY_ADMIN', 'AUDITOR'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus:3,
                    depts: ['130202']
                }
            },
            {
                path: 'graduate',
                component: () => import('@/views/enrollment/graduate'),
                meta: {
                    hidden: false,
                    title: '毕业生审核',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['2'],
                    depts: ['130204']
                }
            },
            {
                path: 'junior',
                component: () => import('@/views/enrollment/junior'),
                meta: {
                    hidden: false,
                    title: '初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    privateeStatus:3,
                    depts: [ '130274', '130209', '130204', '130272', '130403', '130225', '130284', '130224', '130271', '130285','130202','130299','130205']
                }
            },
            {
                path: 'privateTransfer',
                component: () => import('@/views/enrollment/privateTransfer'),
                meta: {
                    hidden: false,
                    title: '区外小学毕业生报名列表',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    period: ['0', '3'],
                    privateeStatus:3,
                    depts: [ '130209']
                }
            },
            {
                path: 'privateTransfers',
                component: () => import('@/views/enrollment/privateTransfers'),
                meta: {
                    hidden: false,
                    title: '区外小学毕业生报名列表',
                    roles: [ 'SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    privateeStatus:3,
                    depts: [ '130209']
                }
            },
            {
                path: 'privatePrimary',
                component: () => import('@/views/privateEnrollment/primary/list'),
                meta: {
                    hidden: false,
                    title: '民办小学报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    privateeStatus:2,
                    depts: ['130299','130205','130284',"130225"]
                }
            },
            {
                path: 'privateJunior',
                component: () => import('@/views/privateEnrollment/junior/list'),
                meta: {
                    hidden: false,
                    title: '民办初中报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    privateeStatus:2,
                    depts: ['130299','130205','130284',"130225"]
                }
            },
            {
                path: 'privateAddAd',
                component: () => import('@/views/privateEnrollment/addAd/index'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN', 'SCHOOL'],
                    isFive: false
                }
            },
            {
                path: 'primaryZx',
                component: () => import('@/views/enrollment/primaryZx'),
                meta: {
                    hidden: false,
                    title: '小学转学报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '2'],
                    depts: ['130274','130272']
                }
            },
            {
                path: 'juniorZx',
                component: () => import('@/views/enrollment/juniorZx'),
                meta: {
                    hidden: false,
                    title: '初中转学报名列表',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    depts: ['130274','130272']
                }
            },
            {
                path: 'addAd',
                component: () => import('@/views/enrollment/addAd/index'),
                meta: {
                    hidden: true,
                    title: '添加报名',
                    roles: ['COUNTY_ADMIN', 'SCHOOL'],
                    isFive: false
                }
            },
            {
                path: 'addAdZx',
                component: () => import('@/views/enrollment/addAdZx/index'),
                meta: {
                    hidden: true,
                    title: '添加转学报名',
                    roles: ['SCHOOL'],
                    isFive: false
                }
            },
            {
                // 古冶区招生 - 毕业小学新增
                path: 'addGuye',
                component: () => import('@/views/enrollment/addGuye/index'),
            },
            {
                // 古冶区招生 - 教育局管理员新增
                path: 'addGuyeAdmin',
                component: () => import('@/views/enrollment/addGuyeAdmin/index'),
            },
            {
                // 丰南区招生 - 学校新增
                path: 'addFengNan',
                component: () => import('@/views/enrollment/addFengNan/index'),
            },
            {
                // 丰南区招生 - 教育局新增
                path: 'addFengNanAdmin',
                component: () => import('@/views/enrollment/addFengNanAdmin/index'),
            },
            {
                // 南堡区招生
                path: 'addNanbao',
                component: () => import('@/views/enrollment/addAdNanBao/index'),
            },
            {
                // 芦台区招生
                path: 'addLuTai',
                component: () => import('@/views/enrollment/addAdLuTai/index'),
            },
            {
                // 路南区招生
                path: 'addLuNan',
                component: () => import('@/views/enrollment/addAdLuNan/index'),
            },
            {
                // 曹妃甸招生
                path: 'addCaoFeiDian',
                component: () => import('@/views/enrollment/addCaoFeiDian/index'),
            },
            {
                path: 'pw_LinZhang',
                component: () => import('@/views/dispatch/linZhang/luQuJieGuoSchool'),
                meta: {
                    hidden: false,
                    title: '派位成功列表',
                    roles: ['SCHOOL'],
                    isFive: false,
                    period: ['0', '3'],
                    depts: ['130423', '130434']
                }
            },
            {
                path: 'adjust',
                component: () => import('@/views/enrollment/adjust'),
                meta: {
                    hidden: false,
                    title: '调剂到本校学生',
                    roles: ['SCHOOL'],
                    isFive: false
                }
            },
        ],
    },
    {
        path: '/dataStatistics',
        component: Layout,
        meta: {
            hidden: false,
            title: '数据统计',
            roles: ['COUNTY_ADMIN'],
            isFive: false
        },
        children: [
            {
                path: 'primary',
                component: () => import('@/views/dataStatistics/primary'),
                meta: {
                    hidden: false,
                    title: '小学统计',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false
                }
            },
            {
                path: 'junior',
                component: () => import('@/views/dataStatistics/junior'),
                meta: {
                    hidden: false,
                    title: '初中统计',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false
                }
            },
            {
                path: 'privatePrimary',
                component: () => import('@/views/dataStatistics/privatePrimary'),
                meta: {
                    hidden: false,
                    title: '民办小学统计',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus:2,
                    depts: ['130299','130205','130225']
                }
            },
            {
                path: 'privateJunior',
                component: () => import('@/views/dataStatistics/privateJunior'),
                meta: {
                    hidden: false,
                    title: '民办初中统计',
                    roles: ['COUNTY_ADMIN'],
                    isFive: false,
                    privateeStatus:2,
                    depts: ['130299','130205','130225']
                }
            },
        ],
    },
    {
        path: '/dataStatisticsSchool',
        component: Layout,
        redirect: '/dataStatisticsSchool/index',
        meta: {
            hidden: false,
            title: '数据统计',
            isFive: false,
            roles: ['SCHOOL']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/dataStatistics/school'),
                meta: {
                    hidden: false,
                    title: '数据统计',
                    isFive: false,
                    roles: ['SCHOOL']
                }
            }
        ],
    },
    {
        path: '/policy',
        component: Layout,
        meta: {
            hidden: false,
            title: '政策公告',
            roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL']
        },
        children: [
            {
                path: 'policyList',
                component: () => import('@/views/policy/policy'),
                meta: {
                    hidden: false,
                    title: '政策公告',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL']
                }
            },
            {
                path: 'homeSetting',
                component: () => import('@/views/policy/homeSetting.vue'),
                meta: {
                    hidden: false,
                    title: '首页设置',
                    roles: ['COUNTY_ADMIN', 'AUDITOR', 'SCHOOL']
                }
            }
        ],
    },
    {
        path: '/dataVerify',
        component: Layout,
        meta: {
            hidden: false,
            title: '数据核对',
            isFive: false,
            roles: ['COUNTY_ADMIN'],
        },
        children: [
            {
                path: 'police',
                component: () => import('@/views/dataVerify/police.vue'),
                meta: {
                    hidden: false,
                    title: '公安信息核对-户口',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'policeResidencePermit',
                component: () => import('@/views/dataVerify/policeResidencePermit.vue'),
                meta: {
                    hidden: false,
                    title: '公安信息核对-居住证',
                    roles: ['COUNTY_ADMIN']
                }
            },
           /* {
                path: 'police',
                component: () => import('@/views/dataVerify/police.vue'),
                meta: {
                    hidden: false,
                    title: '公安信息核对',
                    roles: ['COUNTY_ADMIN']
                }
            },*/
            //房管数据核对报错401公安审核类型不为空
            {
                path: 'property',
                component: () => import('@/views/dataVerify/property.vue'),
                meta: {
                    hidden: false,
                    title: '房管数据核对',
                    roles: ['COUNTY_ADMIN']
                }
            }
            ,
            {
                path: 'propertyZhuJian',
                component: () => import('@/views/dataVerify/propertyZhuJian.vue'),
                meta: {
                    hidden: false,
                    title: '房管数据-住建',
                    roles: ['COUNTY_ADMIN'],
                    depts: [ '130224']
                }
            },
            {
                path: 'propertyZiGui',
                component: () => import('@/views/dataVerify/propertyZiGui.vue'),
                meta: {
                    hidden: false,
                    title: '房管数据-资规',
                    roles: ['COUNTY_ADMIN'],
                    depts: [ '130224']
                }
            }
        ],
    },
    {
        path: '/unitedPolice',
        component: Layout,
        meta: {
            hidden: false,
            title: '报名信息',
            isFive: false,
            roles: ['POLICE'],
            depts: ['130207', '130274']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/unitedDept/police.vue'),
                meta: {
                    hidden: false,
                    isFive: false,
                    title: '报名信息',
                    roles: ['POLICE'],
                    depts: ['130207', '130274']
                }
            },
        ],
    },
    {
        path: '/unitedProperty',
        component: Layout,
        meta: {
            hidden: false,
            title: '报名信息',
            isFive: false,
            roles: ['HOUSE']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/unitedDept/property.vue'),
                meta: {
                    hidden: false,
                    title: '报名信息',
                    roles: ['HOUSE']
                }
            },
        ],
    },
    {
        path: '/setting',
        component: Layout,
        meta: {
            hidden: false,
            title: '设置',
            roles: ['COUNTY_ADMIN', 'CITY_ADMIN']
        },
        children: [
            {
                path: 'studentStatus',
                component: () => import('@/views/setting/studentStatusManage'),
                meta: {
                    hidden: false,
                    title: '毕业小学学籍管理',
                    isFive: false,
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'categoryDetail',
                component: () => import('@/views/setting/categoryDetail'),
                meta: {
                    hidden: false,
                    title: '报名类别详情设置',
                    isFive: false,
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'schoolManage',
                component: () => import('@/views/setting/schoolManage/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校管理',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'schoolRelation',
                component: () => import('@/views/setting/schoolRelation/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校对口关系设置',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130284']
                }
            },
            {
                path: 'secondWhitelist',
                component: () => import('@/views/setting/secondWhitelist/index.vue'),
                meta: {
                    hidden: false,
                    title: '二次白名单管理',
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130284']
                }
            },
            {
                path: 'pwjhs',
                component: () => import('@/views/dispatch/qiuXian/pwjhs'),
                meta: {
                    hidden: false,
                    title: '派位计划数',
                    depts: ['130430'],
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'schoolManageAcct',
                component: () => import('@/views/setting/schoolManage/acct.vue'),
                meta: {
                    hidden: true,
                    title: '学校账号管理',
                    roles: ['COUNTY_ADMIN']
                },
                beforeEnter: (to, from, next) => {
                    if (from.fullPath == '/setting/schoolManage') {
                        next()
                    } else {
                        // 其它路由强制跳转至列表页
                        next('/setting/schoolManage')
                    }
                },
            },
            {
                path: 'eduAudit',
                component: () => import('@/views/setting/eduAuditAcct/index.vue'),
                meta: {
                    hidden: false,
                    title: '教育局审核账号',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'enrollRange',
                component: () => import('@/views/setting/enrollRange/index.vue'),
                meta: {
                    hidden: false,
                    title: '学校招生范围',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'enrollRangeDetail',
                component: () => import('@/views/setting/enrollRange/detail.vue'),
                meta: {
                    hidden: true,
                    title: '学校招生范围详情',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'timeSettingCounty',
                component: () => import('@/views/setting/timeSetting/county.vue'),
                meta: {
                    hidden: false,
                    title: '乡镇报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'timeSettingCity',
                component: () => import('@/views/setting/timeSetting/city.vue'),
                meta: {
                    hidden: false,
                    title: '主城区报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'timeSettingnewCity',
                component: () => import('@/views/setting/timeSetting/newCity.vue'),
                meta: {
                    hidden: false,
                    title: '新城区报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130209']
                }
            },{
                path: 'privateTransfer',
                component: () => import('@/views/setting/timeSetting/privateTransfer.vue'),
                meta: {
                    hidden: false,
                    title: '区外小学毕业报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130209']
                }
            },
            {
                path: 'timeSettingIndustrial',
                component: () => import('@/views/setting/timeSetting/industrial.vue'),
                meta: {
                    hidden: false,
                    title: '工业区报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN'],
                    depts: ['130209']
                }
            },
            {
                path: 'privateTime',
                component: () => import('@/views/setting/timeSetting/privateTime.vue'),
                meta: {
                    hidden: false,
                    title: '民办报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN'],
                    privateeStatus:2,
                    depts: ['130299','130205','130284',"130225"]
                }
            },
            {
                path: 'timeSettingExpiration',
                component: () => import('@/views/setting/timeSetting/expiration.vue'),
                meta: {
                    hidden: false,
                    title: '截止修改报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'privateExpiration',
                component: () => import('@/views/setting/timeSetting/privateExpiration.vue'),
                meta: {
                    hidden: false,
                    title: '民办截止修改报名时间',
                    isFive: false,
                    roles: ['COUNTY_ADMIN'],
                    privateeStatus:2,
                    depts: ['130299','130205','130284',"130225"]
                }
            },
            {
                path: 'areaRiZhi',
                component: () => import('@/views/setting/areaRiZhi/index'),
                meta: {
                    isFive: false,
                    hidden: false,
                    title: '操作日志',
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'transferSwitch',
                component: () => import('@/views/setting/transferSetting/index'),
                meta: {
                    hidden: false,
                    title: '转学报名设置',
                    depts: ['130274','130272'],
                    roles: ['COUNTY_ADMIN']
                }
            },
            {
                path: 'notRightAge',
                component: () => import('@/views/setting/notRightAge'),
                meta: {
                    hidden: false,
                    title: '非适龄儿童报名信息',
                    roles: ['COUNTY_ADMIN'],
                }
            },
            {
                path: 'adSwitchSet',
                component: () => import('@/views/setting/adSwitchSet'),
                meta: {
                    hidden: false,
                    title: '报名开关设置',
                    roles: ['CITY_ADMIN'],
                }
            },

            //区县专用（临漳130423）
            // {
            //   path: 'timeSettingCounty_lz',
            //   component: () => import('@/views/counties/linZhang/setting/linZhangXiangZhenBaoMingShiJian/index'),
            //   meta: {
            //     hidden: false,
            //     title: '乡镇报名时间',
            //     isFive: false,
            //     depts: ['130423'],
            //     roles: ['COUNTY_ADMIN']
            //   }
            // },
            //区县专用（临漳130423）
            // {
            //   path: 'timeSettingCity_lz',
            //   component: () => import('@/views/counties/linZhang/setting/linZhangChengQuBaoMingShiJian/index'),
            //   meta: {
            //     hidden: false,
            //     title: '城区报名时间',
            //     isFive:false,
            //      depts: ['130423'],
            //     roles: ['COUNTY_ADMIN']
            //   }
            // },
            //区县专用（临漳130423）
            // {
            //   path: 'timeSettingExpiration_lz',
            //   component: () => import('@/views/counties/linZhang/setting/jieZhiBaoMingShiJian'),
            //   meta: {
            //     hidden: false,
            //     title: '截止修改报名时间',
            //     isFive: false,
            //     depts: ['130423'],
            //     roles: ['COUNTY_ADMIN']
            //   }
            // },
        ],
    },
    //区县专用（临漳130423）
    // {
    //   path: '/baoMingKa',
    //   component: Layout,
    //   redirect: '/baoMingKa',
    //   meta: {
    //     hidden: false,
    //     title: '报名卡',
    //     isFive: false,
    //     depts: ['130423'],
    //     roles: ['COUNTY_ADMIN']
    //   },
    //   children: [
    //     {
    //       path: 'index',
    //       component: () => import('@/views/counties/linZhang/baoMingKaShengCheng'),
    //       meta: {
    //         hidden: false,
    //         title: '报名卡',
    //         isFive: false,
    //         depts: ['130423'],
    //         roles: ['COUNTY_ADMIN']
    //       }
    //     }
    //   ],
    // },
    {
        path: '/index',
        component: Layout,
        redirect: '/shiJiShouYe/index',
        meta: {
            hidden: false,
            title: '市统计首页',
            isFive: false,
            roles: ['CITY_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/shiTongJi/index'),
                meta: {
                    hidden: false,
                    title: '市统计首页',
                    isFive: false,
                    roles: ['CITY_ADMIN']
                }
            },]
    },
    {
        path: '/userManage',
        component: Layout,
        redirect: '/userManage/index',
        meta: {
            hidden: false,
            title: '用户管理',
            roles: ['CITY_ADMIN']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/userManage/index'),
                meta: {
                    hidden: false,
                    title: '用户管理',
                    roles: ['CITY_ADMIN']
                }
            },
        ],
    },

   /* {
        path: '/clearSignData',
        component: Layout,
        redirect: '/clearSignData/index',
        meta: {
            hidden: false,
            title: '公办清除学生信息',
            roles: ['CITY_ADMIN']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/clearSignData/index'),
                meta: {
                    hidden: false,
                    title: '清除学生信息',
                    roles: ['CITY_ADMIN']
                }
            },
        ],
    },
    {
        path: '/privateClearSignData',
        component: Layout,
        redirect: '/privateClearSignData/index',
        meta: {
            hidden: false,
            title: '民办清除学生信息',
            roles: ['CITY_ADMIN']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/privateClearSignData/index'),
                meta: {
                    hidden: false,
                    title: '清除学生信息',
                    roles: ['CITY_ADMIN']
                }
            },
        ],
    },*/
    {
        path: '/studentList',
        component: Layout,
        redirect: '/studentList/index',
        meta: {
            hidden: false,
            title: '学生列表',
            isFive: false,
            roles: ['CITY_ADMIN']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/studentList/index'),
                meta: {
                    hidden: false,
                    title: '小学学生列表',
                    isFive: false,
                    privateeStatus: 3,
                    period: ['0', '2'],
                    roles: ['CITY_ADMIN']
                }
            },
            {
                path: 'chuZhongStudentList',
                component: () => import('@/views/cityEdu/studentList/chuZhongStudentList'),
                meta: {
                    hidden: false,
                    title: '初中学生列表',
                    isFive: false,
                    period: ['0', '3'],
                    roles: ['CITY_ADMIN']
                }
            },
            {
                path: 'privatePrimary',
                component: () => import('@/views/cityEdu/studentList/privatePrimary'),
                meta: {
                    hidden: false,
                    title: '民办小学学生列表',
                    period: ['0', '2'],
                    roles: ['CITY_ADMIN'],
                }
            },
            {
                path: 'privateJunior',
                component: () => import('@/views/cityEdu/studentList/privateJunior'),
                meta: {
                    hidden: false,
                    title: '民办初中学生列表',
                    period: ['0', '3'],
                    roles: ['CITY_ADMIN'],
                }
            }
        ],
    },
    {
        path: '/tiaoJiJiLuList',
        component: Layout,
        redirect: '/tiaoJiJiLu/index',
        meta: {
            hidden: false,
            title: '调出记录',
            depts: ['130000'],
            roles: ['CITY_ADMIN', 'COUNTY_ADMIN', 'SCHOOL']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/tiaoJiJiLu/index'),
                meta: {
                    hidden: false,
                    title: '调出记录列表',
                    roles: ['CITY_ADMIN', 'COUNTY_ADMIN', 'SCHOOL']
                }
            },
        ],
    },

    {
        path: '/quXianRiZhi',
        component: Layout,
        redirect: '/quXianRiZhi/index',
        meta: {
            hidden: false,
            title: '区县操作日志',
            roles: ['SUPER_ADMIN', 'CITY_ADMIN']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/cityEdu/RiZhi/index'),
                meta: {
                    hidden: false,
                    title: '区县操作日志',
                    roles: ['SUPER_ADMIN', 'CITY_ADMIN']
                }
            },
        ],
    },
    {
        path: '/tiaoJiList',
        component: Layout,
        redirect: '/tiaoJiList/index',
        meta: {
            hidden: false,
            title: '调入列表',
            // isFive: true,
            roles: ['COUNTY_ADMIN', 'SCHOOL']
        },
        //, 'SCHOOL'
        children: [
            {
                path: 'index',
                component: () => import('@/views/tiaoJiLieBiao/xiaoXueTiaoJiLieBiao/index'),
                meta: {
                    hidden: false,
                    title: '公办调入列表',
                    privateeStatus: 3,
                    roles: ['COUNTY_ADMIN', 'SCHOOL']
                }
            },
            {
                path: 'privateIndex',
                component: () => import('@/views/tiaoJiLieBiao/privateTiaoJiLieBiao/index'),
                meta: {
                    hidden: false,
                    title: '民办调入列表',
                    privateeStatus: 2,
                    roles: ['COUNTY_ADMIN', 'SCHOOL'],
                    depts: ['130299','130205','130284','130225']
                }
            },
            // {
            //   path: 'chuZhongStudentList',
            //   component: () => import('@/views/tiaoJiLieBiao/chuZhongTiaoJiLieBiao/index'),
            //   meta: {
            //     hidden: false,
            //     title: '初中调入列表',
            //     roles: [ 'COUNTY_ADMIN', 'SCHOOL']
            //   }
            // }
        ],
    },
    {
        path: '/dispatch',
        component: Layout,
        redirect: '/dispatch/assignmentIndex',
        meta: {
            hidden: false,
            title: '电脑派位',
            // isFive: true,
            depts: ['130430', '130423', '130434', '130408', '130435','130225'],
            roles: ['LOCATION']
        },
        //, 'SCHOOL'
        children: [{
            path: 'assignmentIndex',
            component: () => import('@/views/dispatch/qiuXian/assignmentIndex'),
            meta: {
                hidden: false,
                title: '电脑派位',
                depts: ['130430'],
                roles: ['LOCATION']
            }
        },
            // {
            //   path: 'pwjhs',
            //   component: () => import('@/views/dispatch/qiuXian/pwjhs'),
            //   meta: {
            //     hidden: false,
            //     title: '派位计划数',
            //     depts: ['130430'],
            //     roles: [ 'LOCATION']
            //   }
            // },
            {
                path: 'luQUJieGuo',
                component: () => import('@/views/dispatch/qiuXian/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130430'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentLocal',
                component: () => import('@/views/dispatch/qiuXian/assignmentLocal'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentSchoolList',
                component: () => import('@/views/dispatch/qiuXian/assignmentSchoolList'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment',
                component: () => import('@/views/dispatch/qiuXian/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignmentEndList',
                component: () => import('@/views/dispatch/qiuXian/assignmentEndList'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130430'],
                    roles: ['LOCATION']
                }
            },
            //广宗 派位
            {
                path: 'assignmentIndex_leTing',
                component: () => import('@/views/dispatch/leTing/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130225'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_leTing',
                component: () => import('@/views/dispatch/leTing/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130225'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoleTing',
                component: () => import('@/views/dispatch/leTing/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130225'],
                    roles: ['LOCATION']
                }
            },
            //临漳,魏县电脑派位
            {
                path: 'assignmentIndex_linzhang',
                component: () => import('@/views/dispatch/linZhang/assignmentIndex'),
                meta: {
                    hidden: false,
                    title: '电脑派位',
                    depts: ['130423', '130434'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'assignment_linzhang',
                component: () => import('@/views/dispatch/linZhang/assignment'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130423', '130434'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'luQUJieGuoLinZhang',
                component: () => import('@/views/dispatch/linZhang/luQuJieGuo'),
                meta: {
                    hidden: false,
                    title: '派位录取结果',
                    depts: ['130423', '130434'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwIndexYN',
                component: () => import('@/views/dispatch/yongNian/index'),
                meta: {
                    hidden: false,
                    title: '派位首页',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwSchoolListYN',
                component: () => import('@/views/dispatch/yongNian/school'),
                meta: {
                    hidden: false,
                    title: '派位学校',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwOperateYN',
                component: () => import('@/views/dispatch/yongNian/operate'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwResultYN',
                component: () => import('@/views/dispatch/yongNian/result'),
                meta: {
                    hidden: true,
                    title: '派位结果',
                    depts: ['130408'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwIndexQZ',
                component: () => import('@/views/dispatch/quZhou/index'),
                meta: {
                    hidden: false,
                    title: '派位首页',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwSchoolListQZ',
                component: () => import('@/views/dispatch/quZhou/school'),
                meta: {
                    hidden: false,
                    title: '派位学校',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwOperateQZ',
                component: () => import('@/views/dispatch/quZhou/operate'),
                meta: {
                    hidden: true,
                    title: '电脑派位',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
            {
                path: 'pwResultQZ',
                component: () => import('@/views/dispatch/quZhou/result'),
                meta: {
                    hidden: true,
                    title: '派位结果',
                    depts: ['130435'],
                    roles: ['LOCATION']
                }
            },
        ]
    },
    {
        path: '/my',
        component: Layout,
        redirect: '/my/index',
        meta: {
            hidden: false,
            title: '个人中心',
            roles: ['SUPER_ADMIN', 'LOCATION', 'COUNTY_ADMIN', 'AUDITOR', 'SCHOOL', 'POLICE', 'CITY_ADMIN',
                'ZHU_JIAN', 'ZI_GUI', 'SI_FA', 'DIAN', 'SHEN_PI', 'SHUI', 'REN_SHE', 'SHI_CHANG']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/my/index.vue'),
                meta: {
                    hidden: false,
                    title: '个人中心',
                    roles: ['LOCATION', 'SUPER_ADMIN', 'COUNTY_ADMIN', 'AUDITOR', 'SCHOOL', 'POLICE', 'HOUSE',
                        'ZHU_JIAN', 'ZI_GUI', 'SI_FA', 'DIAN', 'SHEN_PI', 'SHUI', 'REN_SHE', 'SHI_CHANG', 'CITY_ADMIN'
                    ]
                }
            }
        ],
    },
    {
        path: '/gongAn',
        component: Layout,
        redirect: '/gongAn/index',
        meta: {
            hidden: false,
            title: '公安局审核',
            roles: ['COUNTY_ADMIN', 'POLICE'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/gongAnShenHe/index'),
                meta: {
                    hidden: false,
                    title: '公安局审核',
                    roles: ['COUNTY_ADMIN', 'POLICE'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/zhuJian',
        component: Layout,
        redirect: '/zhuJian/index',
        meta: {
            hidden: false,
            title: '住建局审核',
            roles: ['COUNTY_ADMIN', 'ZHU_JIAN'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/zhuJianJuShenHe/index'),
                meta: {
                    hidden: false,
                    title: '住建局审核',
                    roles: ['COUNTY_ADMIN', 'ZHU_JIAN'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/ziGui',
        component: Layout,
        redirect: '/ziGui/index',
        meta: {
            hidden: false,
            title: '资规局审核',
            roles: ['COUNTY_ADMIN', 'ZI_GUI'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/ziGuiShenHe/index'),
                meta: {
                    hidden: false,
                    title: '资规局审核',
                    roles: ['COUNTY_ADMIN', 'ZI_GUI'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/siFa',
        component: Layout,
        redirect: '/siFa/index',
        meta: {
            hidden: false,
            title: '司法局审核',
            roles: ['COUNTY_ADMIN', 'SI_FA'],
            depts: ['130424']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/siFaShenHe/index'),
                meta: {
                    hidden: false,
                    title: '司法局审核',
                    roles: ['COUNTY_ADMIN', 'SI_FA'],
                    depts: ['130424']
                }
            }
        ],
    },
    {
        path: '/dianLiJu',
        component: Layout,
        redirect: '/dianLiJu/index',
        meta: {
            hidden: false,
            title: '电力局审核',
            roles: ['COUNTY_ADMIN', 'DIAN'],
            depts: ['130424']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/dianLiJuShenHe/index'),
                meta: {
                    hidden: false,
                    title: '电力局审核',
                    roles: ['COUNTY_ADMIN', 'DIAN'],
                    depts: ['130424']
                }
            }
        ],
    },
    {
        path: '/shuiLi',
        component: Layout,
        redirect: '/shuiLi/index',
        meta: {
            hidden: false,
            title: '水利局审核',
            roles: ['COUNTY_ADMIN', 'SHUI'],
            depts: ['130424']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/shuLiJuShenHe/index'),
                meta: {
                    hidden: false,
                    title: '水利局审核',
                    roles: ['COUNTY_ADMIN', 'SHUI'],
                    depts: ['130424']
                }
            }
        ],
    },
    {
        path: '/xingZheng',
        component: Layout,
        redirect: '/xingZheng/index',
        meta: {
            hidden: false,
            title: '行政审批局审核',
            roles: ['COUNTY_ADMIN', 'SHEN_PI'],
            depts: ['130424', '130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/chengAn/xingZhengShenHe/index'),
                meta: {
                    hidden: false,
                    title: '行政审批局审核',
                    roles: ['COUNTY_ADMIN', 'SHEN_PI'],
                    depts: ['130424', '130435']
                }
            }
        ],
    },
    {
        path: '/renShe',
        component: Layout,
        redirect: '/renShe/index',
        meta: {
            hidden: false,
            title: '人社局审核',
            roles: ['COUNTY_ADMIN', 'REN_SHE'],
            depts: ['130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/quZhou/renSheJu/index'),
                meta: {
                    hidden: false,
                    title: '人社局审核',
                    roles: ['COUNTY_ADMIN', 'REN_SHE'],
                    depts: ['130435']
                }
            }
        ],
    },
    {
        path: '/shiChang',
        component: Layout,
        redirect: '/shiChang/index',
        meta: {
            hidden: false,
            title: '市场监督管理局审核',
            roles: ['COUNTY_ADMIN', 'SHI_CHANG'],
            depts: ['130435']
        },
        children: [
            {
                path: 'index',
                component: () => import('@/views/counties/quZhou/shiChangJianDuGuanLiJu/index'),
                meta: {
                    hidden: false,
                    title: '市场监督管理局审核',
                    roles: ['COUNTY_ADMIN', 'SHI_CHANG'],
                    depts: ['130435']
                }
            }
        ],
    },

    {
        path: '/login',
        component: () => import('@/views/login/login.vue'),
        meta: {
            hidden: true
        }
    },
    {
        path: "/bind-wechat",
        component: () => import("@/views/login/bind-wechat.vue"),
        meta: {title: '绑定微信'}
    },
    {
        path: "/bind-wechat-auth",
        component: () => import("@/views/login/bind-wechat-auth.vue"),
        meta: {title: '绑定微信'}
    },
    {
        path: "/login-confirm",
        component: () => import("@/views/login/login-confirm.vue"),
    },
    {
        path: "/login-confirm-auth",
        component: () => import("@/views/login/login-confirm-auth.vue"),
    },
    {
        path: "/sms-confirm",
        component: () => import("@/views/login/sms-confirm.vue"),
        meta: {title: '短信验证'}
    },
    {
        path: '/forgetPwd',
        name: 'forgetPwd',
        component: () => import('../views/login/forgetPwd'),
        meta: {title: '忘记密码'}
    },
    {
        path: '/editPwd',
        name: 'editPwd',
        component: () => import('../views/login/editPwd'),
        meta: {title: '修改密码'}
    },
    {
        path: '/404',
        component: () => import('@/views/404.vue'),
        meta: {
            hidden: true,
            title: '404'
        }
    },
    {
        path: '/weiXinTp',
        component: () => import('@/views/weiXinTp'),
        meta: {
            hidden: true,
        }
    },
    {
        path: '*',
        redirect: '/404'
    }
]

/**
 * 设置路由名称
 * @param {*} route
 */
const setRouteName = (route) => {
    if (!route.name) {
        route.name = route.meta?.title || route.path.replace('/', '-') || 'Index'
    }
    if (route.children && route.children.length > 0) {
        for (const item of route.children) {
            setRouteName(item)
        }
    }
}

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location, resolve, reject) {
    if (resolve || reject) {
        return originalPush.call(this, location, resolve, reject)
    }
    return originalPush.call(this, location).catch(() => {
    })
}

for (const route of routes) {
    setRouteName(route)
}


const router = new VueRouter({
    mode: 'hash',
    base: '/admin',
    routes,
    scrollBehavior: () => ({ y: 0 }),
})

export default router
