import { login, logout } from '@/api/login'

const getDefaultState = () => ({
  token: null,
  userInfo: null,
  role: null,
  isFive: null,
  isBind: null,
  isDefaultPassword: null,
  isLoginConfirm: null,
  securityKey: null,// 微信扫码确认登录后获取
  deptCode: null,   // 区县编码
  period: null      ,// 学段,
	mobile: '',
  banJi:''
})
const state = getDefaultState()

const mutations = {
  SET_TOKEN(state, val) {
    state.token = val
  },
  SET_USERINFO(state, val) {
    state.userInfo = val
  },
  SET_ROLE(state, val) {
    state.role = val
  },
  SET_ISFIVE(state, val) {
    state.isFive = val
  },
  SET_ISBIND(state, val) {
    state.isBind = val
  },
  SET_ISDEFAULTPASSWORD(state, val) {
    state.isDefaultPassword = val
  },
  SET_ISLOGINCONFIRM(state, val) {
    state.isLoginConfirm = val
  },
  SET_SECURITYKEY(state, val) {
    state.securityKey = val
  },
  SET_DEPTCODE(state, val) {
    state.deptCode = val
  },
  SET_PERIOD(state, val) {
    state.period = val
  },
  SET_RESETSTATE(state) {
    state = Object.assign(state, getDefaultState())
  },
	setMobile(state, v) {
		state.mobile = v
	}
  ,
  SET_BANJI(state, v) {
    state.banJi = v
  }
}
const actions = {
  login({ commit }, form) {
    return new Promise((resolve, reject) => {
      login(form).then(data => {
        commit('SET_TOKEN', data.token)
        commit('SET_USERINFO', data)
        commit('SET_ROLE', data.roleCode)
        commit('SET_ISFIVE', data.isFive)
        commit('SET_ISBIND', data.binding)
        commit('SET_ISDEFAULTPASSWORD', data.defaultPasswordFlag)
        commit('SET_DEPTCODE', data.deptCode)
        commit('SET_BANJI', data.banJi)
        commit('SET_PERIOD', data.schoolType ? data.schoolType : '0')
				commit('setMobile', data.mobile)
				// 正常登录扫码后返回securityKey，作为一个额外请求头信息存下
				// 但是海港区没有扫码验证这一步，故直接存下securityKey
				if (data.deptCode == '130274') {
					commit('SET_SECURITYKEY', data.state)
				}
        resolve(data)
      }).catch(err => {
        reject(err)
      })
    })
  },
  async logout({ commit }) {
    return new Promise((resolve) => {
      logout()
        .finally(() => {
          commit('SET_RESETSTATE', null)
          resolve()
        })
    })

  },
  clearState({ commit }) {
    commit('SET_RESETSTATE', null)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}