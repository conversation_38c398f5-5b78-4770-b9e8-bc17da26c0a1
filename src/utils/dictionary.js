// 与学生关系
let relationList = [
	{ id: '1',  val: '父子' },
	{ id: '2',  val: '父女' },
	{ id: '3',  val: '母子' },
	{ id: '4',  val: '母女' },
	{ id: '5',  val: '本人' },
	{ id: '6',  val: '其他' },
	{ id: '7',  val: '祖父母' },
	{ id: '8',  val: '外祖父母' },
	{ id: '9',  val: '弟弟' },
	{ id: '10', val: '妹妹' },
	{ id: '11', val: '哥哥' },
	{ id: '12', val: '姐姐' }
]

// 性别
let genderList = [
	{ id: '1', val: '男' },
	{ id: '2', val: '女' },
]

// 是否
let booleanList = [
	{ id: '0', val: '否' },
	{ id: '1', val: '是' },
]

// 户籍类型
let domicileTypeList = [
	{ id: '1', val: '农业' },
	{ id: '2', val: '非农业' },
]
//居住证信息
let residenceTypeList = [
	{ id: '1', val: '居住证' },
	{ id: '2', val: '居住登记凭证' }
]
let JuzhuList = [
	{ id: '0', val: '居住证' },
	{ id: '1', val: '居住凭证' },
	{ id: '2', val: '居住证明' },
]
//56个民族
let minZuList = [
	{ id: '1', val: '汉族' },
	{ id: '2', val: '壮族' },
	{ id: '3', val: '回族' },
	{ id: '4', val: '满族' },
	{ id: '5', val: '维吾尔族' },
	{ id: '6', val: '苗族' },
	{ id: '7', val: '彝族' },
	{ id: '8', val: '土家族' },
	{ id: '9', val: '藏族' },
	{ id: '10', val: '蒙古族' },
	{ id: '11', val: '侗族' },
	{ id: '12', val: '布依族' },
	{ id: '13', val: '瑶族' },
	{ id: '14', val: '白族' },
	{ id: '15', val: '朝鲜族' },
	{ id: '16', val: '哈尼族' },
	{ id: '17', val: '黎族' },
	{ id: '18', val: '哈萨克族' },
	{ id: '19', val: '傣族' },
	{ id: '20', val: '畲族' },
	{ id: '21', val: '傈僳族' },
	{ id: '22', val: '东乡族' },
	{ id: '23', val: '仡佬族' },
	{ id: '24', val: '拉祜族' },
	{ id: '25', val: '佤族' },
	{ id: '26', val: '水族' },
	{ id: '27', val: '纳西族' },
	{ id: '28', val: '羌族' },
	{ id: '29', val: '土族' },
	{ id: '30', val: '仫佬族' },
	{ id: '31', val: '锡伯族' },
	{ id: '32', val: '柯尔克孜族' },
	{ id: '33', val: '景颇族' },
	{ id: '34', val: '达斡尔族' },
	{ id: '35', val: '撒拉族' },
	{ id: '36', val: '布朗族' },
	{ id: '37', val: '毛南族' },
	{ id: '38', val: '塔吉克族' },
	{ id: '39', val: '普米族' },
	{ id: '40', val: '阿昌族' },
	{ id: '41', val: '怒族' },
	{ id: '42', val: '鄂温克族' },
	{ id: '43', val: '京族' },
	{ id: '44', val: '基诺族' },
	{ id: '45', val: '德昂族' },
	{ id: '46', val: '保安族' },
	{ id: '47', val: '俄罗斯族' },
	{ id: '48', val: '裕固族' },
	{ id: '49', val: '乌孜别克族' },
	{ id: '50', val: '门巴族' },
	{ id: '51', val: '鄂伦春族' },
	{ id: '52', val: '独龙族' },
	{ id: '53', val: '赫哲族' },
	{ id: '54', val: '高山族' },
	{ id: '55', val: '珞巴族' },
	{ id: '56', val: '塔塔尔族' },
	{ id: '57', val: '其他' }
]
let banjiList = [
	{ id: '1', val: '1班' },
	{ id: '2', val: '2班' },
	{ id: '3', val: '3班' },
	{ id: '4', val: '4班' },
	{ id: '5', val: '5班' },
	{ id: '6', val: '6班' },
	{ id: '7', val: '7班' },
	{ id: '8', val: '8班' },
	{ id: '9', val: '9班' },
	{ id: '10', val: '10班' },
	{ id: '11', val: '11班' },
	{ id: '12', val: '12班' },
	{ id: '13', val: '13班' },
	{ id: '14', val: '14班' },
	{ id: '15', val: '15班' },
	{ id: '16', val: '16班' },
	{ id: '17', val: '17班' },
	{ id: '18', val: '18班' },
	{ id: '19', val: '19班' },
	{ id: '20', val: '20班' }
]
// 年级
let gradeList = [
	{ id: '1', val: '一年级' },
	{ id: '2', val: '二年级' },
	{ id: '3', val: '三年级' },
	{ id: '4', val: '四年级' },
	{ id: '5', val: '五年级' },
	{ id: '6', val: '六年级' },
	{ id: '7', val: '初一' },
	{ id: '8', val: '初二' },
	{ id: '9', val: '初三' }
]

let changeList = [
	{ id: '1', val: '乡镇' },
	{ id: '2', val: '城区' },
]
// 验证规则们
let rulesList = [
	// 0：无格式限制
	{
		
	},
	// 1：身份证号
	// validator需要去报名页面引用，在这里引用无法生效
	{
		required: true,
		trigger: 'blur'
	},
	// 2：出生日期
	{
		required: true,
		message: '请选择出生日期',
		trigger: 'change'
	},
	// 3：性别
	{
		required: true,
		message: '请选择性别',
		trigger: 'change',
		list: genderList
	},
	// 4：是否
	{
		required: true,
		message: '请选择',
		trigger: 'change',
		list: booleanList
	},
	// 5：一年级至初三
	{
		required: true,
		message: '请选择年级',
		trigger: 'change',
		list: gradeList
	},
	// 6：选择小学，学校列表
	{
		required: true,
		message: '请选择小学',
		trigger: 'change',
		list: []
	},
	// 7：选择幼儿园，学校列表
	{
		required: true,
		message: '请选择幼儿园',
		trigger: 'change',
		list: []
	},
	// 8：选择学校招生范围，小区列表
	{
		required: true,
		message: '请选择',
		trigger: 'change',
		list: []
	},
	// 9：选择关系：父子，父女，母子，母女，本人
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0, 5)
	},
	// 10：选择关系：父子，父女，母女，母子，其他
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0, 4).concat(relationList[5])
	},
	// 11：手机号
	// 同身份证号，validator需要去报名页面引用，在这里引用无法生效
	{
		required: true,
		trigger: 'blur'
	},
	// 12：选择关系：弟弟，妹妹，哥哥，姐姐
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(8)
	},
	// 13：选择关系：父子，父女，母子，母女，祖父母，外祖父母，本人
	{
		required: true,
		message: '请选择关系',
		trigger: 'change',
		list: relationList.slice(0, 4).concat(relationList[6], relationList[7], relationList[4], relationList[5])
	},
	// 14：日期时间
	{
		required: true,
		message: '请选择时间',
		trigger: 'change'
	},
	// 15：图片验证
	{
		required: true,
		message: '请上传图片',
		trigger: 'change'
	},
	// 16：户籍类型
	{
		required: true,
		message: '请选择',
		trigger: 'change',
		list: domicileTypeList
	},
	// 17：居住证类型
	{
		required: true,
		message: '请选择',
		trigger: 'change',
		list: residenceTypeList
	},
	// 18：56个民族
	{
		required: true,
		message: '请选择',
		trigger: 'change',
		list: minZuList
	},
	// 19：意向地
	{
		required: true,
		message: '请选择意向地',
		trigger: 'change',
		list:[]
	},
	// 20 乡镇或者城区
	{
		required: true,
		message: '请选择',
		trigger: 'change',
		list: changeList
	},
	// 21 片区学校
	{
		required: true,
		message: '请选择小学',
		trigger: 'change',
		list: []
	},
	// 22
	{
		required: true,
		message: '请选择小区',
		trigger: 'change',
		list: []
	},
	// 23
	{
		required: true,
		message: '请选择小区',
		trigger: 'change',
		list: []
	},
	// 24：班级管理
	{
		required: true,
		message: '请选择班级',
		trigger: 'change',
		list: banjiList
	},
	// 25：请现在居住类型
	{
		required: true,
		message: '请现在居住类型',
		trigger: 'change',
		list: JuzhuList
	},
]

// 所有区县
let areaList = [
	{ id: '130202', val: '路南区' },
	{ id: '130203', val: '路北区' },
	{ id: '130204', val: '古冶区' },
	{ id: '130403', val: '开平区' },
	{ id: '130208', val: '丰润区' },
	{ id: '130207', val: '丰南区' },
	{ id: '130281', val: '遵化市' },
	{ id: '130229', val: '玉田县' },
	{ id: '130283', val: '迁安市' },
	{ id: '130227', val: '迁西县' },
	{ id: '130284', val: '滦州市' },
	{ id: '130224', val: '滦南县' },
	{ id: '130225', val: '乐亭县' },
	{ id: '130209', val: '曹妃甸区' },
	{ id: '130271', val: '芦台开发区' },
	{ id: '130272', val: '汉沽管理区' },
	{ id: '130273', val: '高新开发区' },
	{ id: '130274', val: '海港开发区' },
	{ id: '130285', val: '南堡开发区' }
]


// 随迁子女-居住信息枚举
let liveRelationList = [
	{ id: '1',  val: '父子' },
	{ id: '2',  val: '父女' },
	{ id: '3',  val: '母子' },
	{ id: '4',  val: '母女' },
]



// 表单域的inputItemCode字段的值对应的验证规则
// id: inputItemCode字段的值，val: 验证规则里的trigger类型，valCn：验证规则里的message中文
let triggerTpByCode = [
	{
		id: '1',
		val: 'blur',
		valCn: '输入'
	},
	{
		id: '2',
		val: 'change',
		valCn: '选择'
	},
	{
		id: '3',
		val: 'change',
		valCn: '选择'
	},
	{
		id: '4',
		val: 'blur',
		valCn: '输入'
	},
	{
		id: '5',
		val: 'change',
		valCn: '选择'
	},
	{
		id: '6',
		val: 'change',
		valCn: '选择'
	}
]

// 邱县 - 需要填志愿的报名类别们
let wishRequiredSetUpIds = ['82', '83', '84']

// 永年 - 第3批报名入口, 不显示学校报名入口的setUpId们
let schoolEnrtryNeedHide = ['29', '30', '31']

// 鸡泽 - 城区初中不选学校
let dontNeedSchoolJiZe = ['81', '82', '83', '84', '85', '86', '87', '88', '108', '109', '110', '111', '112', '113', '114', '115']

export {
	relationList,
	rulesList,
	genderList,
	booleanList,
	gradeList,
	areaList,
	triggerTpByCode,
	wishRequiredSetUpIds,
	schoolEnrtryNeedHide,
	dontNeedSchoolJiZe,
	liveRelationList,
	minZuList,
	residenceTypeList,
	domicileTypeList,
	changeList,
	banjiList,
	JuzhuList
}