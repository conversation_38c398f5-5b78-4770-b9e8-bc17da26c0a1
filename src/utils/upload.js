import { MessageBox, Message } from "element-ui";
import { uploadImg } from "@/api/common";
import * as imageConversion from "image-conversion";
import store from "@/store";

export const uploaDing = (file) => {
  const isJPG = file.type;
  const isLt2M = file.size / 1024 / 1024 < 6;

  if (!isLt2M) {
    Message.error("上传图片大小不能超过 6Mb!");
    return isLt2M;
  }
  if (isJPG === "image/jpeg" || isJPG === "image/png") {
    // 大于500k压缩
    if (file.size / 1024 > 200) {
      return new Promise((resolve, reject) => {
        imageConversion.compressAccurately(file, 200).then((res) => {
          resolve(res);
        });
      });
    } else {
      return true;
    }
  } else {
    Message.error("请上传jpg或者png格式图片");
    return false;
  }
};
export const uploadPicture = async (param, prefixDeptCode) => {
  const formData = new FormData();
  formData.append("file", param.file);
  const data = await uploadImg(formData, prefixDeptCode);
  if (data) {
    Message.success("上传成功")
    return data;
  } else {
    MessageBox.alert('上传照片异常，请重试', "提示", {
      confirmButtonText: "确定",
    });
  }
};

// 通用文件上传函数
export const uploadFile = async (formData) => {
  try {
    const data = await uploadImg(formData, store.getters.deptCode);
    if (data) {
      return {
        code: 0,
        data: data,
        message: '上传成功'
      };
    } else {
      return {
        code: -1,
        message: '上传失败'
      };
    }
  } catch (error) {
    console.error('上传文件错误:', error);
    return {
      code: -1,
      message: '上传失败: ' + (error.message || '未知错误')
    };
  }
};
