
// 与学生关系
const StudentRelationship = [
    {
        label: '父子',
        value: '父子',
    },
    {
        label: '母子',
        value: '母子'
    },
    {
        label: '父女',
        value: '父女'
    },
    {
        label: '母女',
        value: '母女'
    },
]

// 优抚信息类型

// 优抚信息类型
export const EntitledGroupType = [
    {
        label: '现役军人子女',
        value: 0,
        fields: [
            {
                label: '持有人姓名',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'name',
                type: 'input',
                placeholder: ' 请输入持有人姓名',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入持有人姓名',
                        trigger: 'change'
                    }
                ]
            },
            {
                label: '证件编号',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'code',
                type: 'input',
                placeholder: ' 请输入证件编号',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入证件编号',
                        trigger: 'change'
                    }
                ]
            },
            {
                label: '发证日期',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'time',
                type: 'dateTime',
                placeholder: ' 请选择发证日期',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择发证日期',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证机关',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'organ',
                type: 'input',
                placeholder: '请输入发证机关',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入发证机关',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '与该生关系',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'relation',
                type: 'select',
                placeholder: '请选择关系',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择关系',
                        trigger: 'blur'
                    }
                ],
                data: StudentRelationship,
            },
            {
                label: '军官证或军士证',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'cert',
                type: 'upload',
                placeholder: '请上传军官证或军士证',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '军官证或军士证不能为空',
                        trigger: 'blur'
                    }
                ],
            },
            {
                label: '补充材料',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'other',
                type: 'upload',
                placeholder: '补充材料（选填）',
                rules: [],
            },
        ],
    },
    {
        label: '烈士子女',
        value: 1,
        fields: [
            {
                label: '烈士姓名',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'name',
                type: 'input',
                placeholder: ' 请输入权利人姓名',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入权利人姓名',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '证件编号',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'code',
                type: 'input',
                placeholder: '请输入证件编号',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入证件编号',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证日期',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'time',
                type: 'dateTime',
                placeholder: ' 请选择发证日期',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择发证日期',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证机关',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'organ',
                type: 'input',
                placeholder: '请输入发证机关',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入发证机关',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '与该生关系',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'relation',
                type: 'select',
                placeholder: '请选择关系',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择关系',
                        trigger: 'blur'
                    }
                ],
                data: StudentRelationship,
            },
            {
                label: '烈士证明书',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'cert',
                type: 'upload',
                placeholder: '请上传烈士证明书',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '烈士证明书不能为空',
                        trigger: 'blur'
                    }
                ],
            },
            {
                label: '补充材料',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'other',
                type: 'upload',
                placeholder: '补充材料（选填）',
                rules: [],
            },
        ]
    },
    {
        label: '公安英模',
        value: 2,
        fields: [
            {
                label: '持有人姓名',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'name',
                type: 'input',
                placeholder: ' 请输入持有人姓名',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入持有人姓名',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证日期',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'time',
                type: 'dateTime',
                placeholder: ' 请选择发证日期',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择发证日期',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证机关',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'organ',
                type: 'input',
                placeholder: '请输入发证机关',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入发证机关',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '与该生关系',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'relation',
                type: 'select',
                placeholder: '请选择关系',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择关系',
                        trigger: 'blur'
                    }
                ],
                data: StudentRelationship,
            },
            {
                label: '公安英模证书',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'cert',
                type: 'upload',
                placeholder: '请上传公安英模证书',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '公安英模证书不能为空',
                        trigger: 'blur'
                    }
                ],
            },
            {
                label: '补充材料',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'other',
                type: 'upload',
                placeholder: '补充材料（选填）',
                rules: [],
            },
        ]
    },
    {
        label: '因公牺牲伤残警察子女（含消防）',
        value: 3,
        fields: [
            {
                label: '持有人姓名',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'name',
                type: 'input',
                placeholder: ' 请输入持有人姓名',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入持有人姓名',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证日期',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'time',
                type: 'dateTime',
                placeholder: ' 请选择发证日期',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择发证日期',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发证机关',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'organ',
                type: 'input',
                placeholder: '请输入发证机关',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入发证机关',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '与该生关系',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'relation',
                type: 'select',
                placeholder: '请选择关系',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择关系',
                        trigger: 'blur'
                    }
                ],
                data: StudentRelationship,
            },
            {
                label: '伤残人民警察证',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'cert',
                type: 'upload',
                placeholder: '请上传伤残人民警察证',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '伤残人民警察证不能为空',
                        trigger: 'blur'
                    }
                ],
            },
            {
                label: '补充材料',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'other',
                type: 'upload',
                placeholder: '补充材料（选填）',
                rules: [],
            },
        ]
    },
    {
        label: '高层次人才随迁子女（凤凰英才卡）',
        value: 4,
        fields: [
            {
                label: '持有人姓名',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'name',
                type: 'input',
                placeholder: ' 请输入持有人姓名',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入持有人姓名',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '英才卡编号',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'code',
                type: 'input',
                placeholder: '请输入英才卡编号',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请输入英才卡编号',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '发卡日期',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'time',
                type: 'dateTime',
                placeholder: ' 请选择发卡日期',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择发卡日期',
                        trigger: 'blur'
                    }
                ]
            },
            {
                label: '与该生关系',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'relation',
                type: 'select',
                placeholder: '请选择关系',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: '请选择关系',
                        trigger: 'blur'
                    }
                ],
                data: StudentRelationship,
            },
            {
                label: 'Ⅰ类凤凰英才卡',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'cert',
                type: 'upload',
                placeholder: '请上传Ⅰ类凤凰英才卡',
                isRequired: true,
                rules: [
                    {
                        required: true,
                        message: 'Ⅰ类凤凰英才卡不能为空',
                        trigger: 'blur'
                    }
                ],
            },
            {
                label: '补充材料',
                value: '',
                isModify: '',
                lastFieldValue:'',
                prop: 'other',
                type: 'upload',
                placeholder: '补充材料（选填）',
                rules: [],
            },
        ]
    }
]
