<template>
  <!-- 报名详情查看：select字段 -->
  <div class="normal-select-ex">
    <span>{{ modelData }}</span>
  </div>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import { deptPageList, schoolAdRange } from "@/api/index"
export default {
  name: 'normal-select-ex',
  data() {
    return {
      modelData: '',
      fieldVal: '',
      applyRes: this.$store.state.applyQryResult,
    }
  },
  props: {
    // 字段所有配置
    itemConfig: {
      type: Object,
      required: true
    }
  },
  computed: {
    // 验证规则的快捷方式
    verCode() {
      return this.itemConfig.infoVerificationCode
    }
  },
  created() {
    this.fieldVal = this.itemConfig.fieldValue
    this.modelData = this.fieldVal
    // infoVerificationCode为3，4，5，9，10，12，13，16时直接读取字典里的list
    let dicListIdx = [3, 4, 5, 9, 10, 12, 13, 16]
    // infoVerificationCode为6，7，8时需要请求api获取数据
    let qryListIdx = [6, 7, 8]
    if (dicListIdx.indexOf(this.verCode) != -1) {
      // 特殊处理：南堡开发区(130285)的"与该生关系"字段使用规则10的关系列表
      let targetList = rulesList[this.verCode].list
      if (this.$store.getters.deptCode === '130285' && this.verCode === 16 && this.itemConfig.fieldName === '与该生关系') {
        targetList = rulesList[10].list
      }

      let matchItem = targetList.filter(v => v.id == this.fieldVal)
      if (matchItem.length > 0) {
        this.modelData = matchItem[0].val
      } else {
        this.modelData = ''
      }
    } else if (qryListIdx.indexOf(this.verCode) != -1) {
      if (this.verCode == 6) {
        this.primarySchoolList()
      } else if (this.verCode == 7) {
        this.preSchoolList()
      } else if (this.verCode == 8) {
        this.rangeList()
      }
    }
  },
  methods: {
    // infoVerificationCode == 6时，小学列表
    primarySchoolList() {
      let params = {
        keywords: "",
        nature: '',
        // 学段
        period: 2,
        deptCode: this.$store.state.deptCode,
        type: 1,
        pageNumber: 1,
        pageSize: 9999
      }
      deptPageList(params).then(res => {
        res.records.forEach(v => {
          if (v.id == this.fieldVal) {
            this.modelData = v.deptName
          }
        })
      })
    },
    // infoVerificationCode == 7时，幼儿园列表
    preSchoolList() {
      let params = {
        keywords: "",
        nature: '',
        // 学段
        period: 1,
        deptCode: this.$store.state.deptCode,
        type: 1,
        pageNumber: 1,
        pageSize: 9999
      }
      deptPageList(params).then(res => {
        res.records.forEach(v => {
          if (v.id == this.fieldVal) {
            this.modelData = v.deptName
          }
        })
      })
    },
    // infoVerificationCode == 8时，范围列表
    rangeList() {
      let params = {
        type: 2,
        schoolId: this.applyRes.enrollSchoolId
      }
      schoolAdRange(params).then(res => {
        res.forEach(v => {
          if (v.id == this.fieldVal) {
            this.modelData = v.rangeName
          }
        })
      })
    },
  }
}
</script>

<style>
</style>