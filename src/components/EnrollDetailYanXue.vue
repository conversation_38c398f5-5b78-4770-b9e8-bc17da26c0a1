<template>
  <div class="enroll-detail">
    <el-skeleton
        :loading="loadingAdForm"
        animated
    >
      <template slot="default">


        <div v-if="$store.getters.deptCode === '130204' && listItemDetail.graduateSchool"
             class="graduate-school"
             style="margin-bottom: 10px; padding: 10px; background: #f1f1f1; border-radius: 5px;">
          毕业小学：{{ listItemDetail.graduateSchool }}
        </div>

        <dl class="form-group" v-if="isTransfer">
          <dt class="f-group-title">转学信息</dt>
          <dd class="f-group-detail">
            <ul class="f-g-d-info-list">
              <li class="f-g-d-item normal-item">
                <div class="desc-txt">转出学校：</div>
                <div class="res-txt">{{transfer.fromDeptName}}</div>
              </li>
              <li class="f-g-d-item normal-item">
                <div class="desc-txt">转出学校年级：</div>
                <div class="res-txt">{{transfer.fromGradeYear}}</div>
              </li>
              <li class="f-g-d-item normal-item">
                <div class="desc-txt">转入学校：</div>
                <div class="res-txt">{{transfer.toDeptName}}</div>
              </li>
              <li class="f-g-d-item normal-item">
                <div class="desc-txt">转入学校年级：</div>
                <div class="res-txt">{{transfer.toGradeYear}}</div>
              </li>
            </ul>
          </dd>
        </dl>

        <!-- 普通字段 -->
        <dl v-for="item, idx in normalForm" :key="item.typeConfigId" class="form-group">
          <dt class="f-group-title">{{ item.infoName }}</dt>
          <dd class="f-group-detail">
            <div>
              <ul class="f-g-d-info-list">
                <li v-for="fi, fidx in item._normalItem" :key="fidx" class="f-g-d-item normal-item">
                  <div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
                  <div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi"
                       v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}
                  </div>
                  <div class="res-txt"
                       v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
                    <normal-select-ex :item-config.sync="fi" :list-item-config.sync="listItemDetail"></normal-select-ex>
                  </div>
                </li>
                <!--   古冶区定制化   start       -->
<!--                <li v-if="$store.getters.deptCode === '130204' && listItemDetail.graduateSchool" >
                  <div class="res-txt">
                    <div class="desc-txt">毕业小学：{{ listItemDetail.graduateSchool }} </div>
                  </div>
                </li>-->
                <!--   古冶区定制化   end       -->
              </ul>
            </div>
            <div>
              <!-- <ul class="f-g-d-info-list">
                <li v-for="fi, fidx in item._imgItem" :key="fidx" class="f-g-d-item img-item">
                  <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                </li>
              </ul> -->
              <viewer :images="switchImgList(item._imgItem)">
                <ul class="f-g-d-info-list">
                  <li v-for="(fi, fidx) in switchImgList(item._imgItem)" :key="fidx" class="f-g-d-item img-item">
                    <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px"/>
                    <div :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}</div>
                  </li>
                </ul>
              </viewer>
            </div>
          </dd>
        </dl>
        <!-- 双胞胎 -->
        <dl class="form-group" v-if="siblings.list.length > 0">
          <dt class="f-group-title">双胞胎信息</dt>
          <dd class="f-group-detail">
            <ul class="sib-list">
              <li class="sib-item" v-for="si, sIdx in siblings.list" :key="si.typeConfigId">
                <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
                <div>
                  <ul class="f-g-d-info-list">
                    <li v-for="fi, fidx in si._normalItem" :key="fidx" class="f-g-d-item normal-item">
                      <div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
                      <div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi"
                           v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}
                      </div>
                      <div class="res-txt"
                           v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
                        <normal-select-ex :item-config.sync="fi"
                                          :list-item-config.sync="listItemDetail"></normal-select-ex>
                      </div>
                    </li>
                  </ul>
                </div>
                <div>
                  <!-- <ul class="f-g-d-info-list">
                    <li v-for="fi, fidx in si._imgItem" :key="fidx" class="f-g-d-item img-item">
                      <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                    </li>
                  </ul> -->
                  <viewer :images="switchImgList(si._imgItem)">
                    <ul class="f-g-d-info-list">
                      <li v-for="(fi, fidx) in switchImgList(si._imgItem)" :key="fidx" class="f-g-d-item img-item">
                        <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px"/>
                        <div :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}</div>
                      </li>
                    </ul>
                  </viewer>
                </div>
              </li>
            </ul>
          </dd>
        </dl>
        <!-- 房产 -->
        <template v-if="propertyForm.list.length > 0">
          <dl v-for="ppItem, ppIdx in propertyForm.list" :key="ppItem.typeConfigId" class="form-group">
            <dt class="f-group-title f-group-title-pp">
              <span>房产信息</span>
              <span>{{ propertyForm.list[0].infoName }}</span>
            </dt>
            <dd class="f-group-detail">
              <div>
                <ul class="f-g-d-info-list">
                  <li v-for="fi, fidx in ppItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
                    <div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
                    <div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi"
                         v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}
                    </div>
                    <div class="res-txt"
                         v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
                      <normal-select-ex :item-config.sync="fi"
                                        :list-item-config.sync="listItemDetail"></normal-select-ex>
                    </div>
                  </li>
                </ul>
              </div>
              <div>
                <!-- <ul class="f-g-d-info-list">
                  <li v-for="fi, fidx in ppItem._imgItem" :key="fidx" class="f-g-d-item img-item">
                    <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                  </li>
                </ul> -->
                <viewer :images="switchImgList(ppItem._imgItem)">
                  <ul class="f-g-d-info-list">
                    <li v-for="(fi, fidx) in switchImgList(ppItem._imgItem)" :key="fidx" class="f-g-d-item img-item">
                      <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px"/>
                      <div :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}</div>
                    </li>
                  </ul>
                </viewer>
              </div>
            </dd>
          </dl>
        </template>
        <!-- 其它 -->
        <template v-if="others.list.length > 0">
          <dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
            <dt class="f-group-title" v-if="prefixDeptCode == '130481'">乡镇购房材料</dt><!-- 武安 -->
            <dt class="f-group-title" v-else>其他材料证明</dt>
            <dd class="f-group-detail">
              <div>
                <ul class="f-g-d-info-list">
                  <li v-for="fi, fidx in oItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
                    <div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
                    <div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi"
                         v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}
                    </div>
                    <div class="res-txt"
                         v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
                      <normal-select-ex :item-config.sync="fi"
                                        :list-item-config.sync="listItemDetail"></normal-select-ex>
                    </div>
                  </li>
                </ul>
              </div>
              <!-- <ul class="f-g-d-info-list">
                <li v-for="fi, fidx in oItem._imgItem" :key="fidx" class="f-g-d-item img-item">
                  <normal-img-ex :item-config.sync="fi"></normal-img-ex>
                </li>
              </ul> -->
              <viewer :images="switchImgList(oItem._imgItem)">
                <ul class="f-g-d-info-list">
                  <li v-for="(fi, fidx) in switchImgList(oItem._imgItem)" :key="fidx" class="f-g-d-item img-item">
                    <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px"/>
                    <div :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}</div>
                  </li>
                </ul>
              </viewer>
            </dd>
          </dl>
        </template>

        <!-- 随迁居住证信息 -->
        <dl class="form-group" v-if="liveForm && liveForm.liveType === 0 || liveForm && liveForm.liveType === 1">
          <dt class="f-group-title">居住证信息</dt>
          <dd class="f-group-detail">
            <ul class="f-g-d-info-list">
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">居住证类型：</div>
                <div class="res-txt" v-if="liveForm.liveType === 0 ">唐山各区户籍</div>
                <div class="res-txt" v-else>唐山各县及外省市户籍</div>
              </li>
            </ul>

            <ul class="f-g-d-info-list" v-if="liveForm.liveType===1">
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">居住证编号：</div>
                <div class="res-txt">{{liveForm.liveOption.code}}</div>
              </li>
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">居住证地址：</div>
                <div class="res-txt">{{liveForm.liveOption.address}}</div>
              </li>
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">居住证持有者：</div>
                <div class="res-txt">{{liveForm.liveOption.holder}}</div>
              </li>
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">持有者身份证号：</div>
                <div class="res-txt">{{liveForm.liveOption.idCard}}</div>
              </li>
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">与学生关系：</div>
                <div class="res-txt">{{liveForm.liveOption.relation}}</div>
              </li>
            </ul>

            <ul class="f-g-d-info-list">
              <li  class="f-g-d-item normal-item">
                <div class="desc-txt">居住凭证：</div>
                <normal-img-ex :item-config="{'fieldId':'liveCert','fieldValue': liveCert}"></normal-img-ex>
              </li>
            </ul>
          </dd>
        </dl>

        <dl class="form-group" v-if="entitledGroupFlag">
          <dt class="f-group-title">优抚类型</dt>
          <dd class="f-group-detail">
            <ul class="f-g-d-info-list entitled-group-list">
              <li v-for="(item,index) in entitledGroupForm.fields" :key="index" class="f-g-d-item normal-item">
                <div class="entitled-item" :class="{ 'is-modify': item.isModify == 1 }">
                  <span class="desc-txt" >{{ item.label }}：</span>
                  <span class="res-txt" v-if="item.type !== 'upload'">{{ item.value }}</span>
                  <normal-img-ex v-else :itemConfig="{filedName: item.label, fieldId: item.prop,fieldValue: item.value}" />
                </div>
              </li>
            </ul>
          </dd>
        </dl>

      </template>
    </el-skeleton>
  </div>
</template>

<script>
import {adFormDetail, getTransfer} from "@/api/enrollment.js"
import NormalSelectEx from "@/components/Exhibition/NormalSelectEx"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
import {imgPrefix} from '@/utils/common'
import liveForm from "@/components/liveForm.vue";

export default {
  name: 'enroll-detail',
  components: {
    NormalSelectEx,
    NormalImgEx,
    liveForm
  },
  data() {
    return {
      // 是否转学
      isTransfer: false,
      prefixDeptCode: this.$store.getters.deptCode,
      imgPrefix: imgPrefix(),
      // 加载中
      loadingAdForm: true,
      // 报名人
      stuId: '',
      // 报名的学校
      adSchool: '',
      // 整个页面循环用的list
      originList: [],
      // 非房产非双胞胎的普通字段
      normalForm: [],
      // 房产
      propertyForm: {
        list: []
      },
      // 多胞胎
      siblings: {
        list: []
      },
      // 其他补充信息
      others: {
        list: []
      },
      // 报名列表当前行的详情
      listItemDetail: {},
      liveForm: {},
      liveCert: '',
      entitledGroupForm:{
        value: '',
        fields: []
      },
      transfer:{},//转学信息
      entitledGroupFlag: false,

    }
  },
  props: {
    stuDetail: {
      required: true,
      type: Object
    }
  },
  computed: {
    // viewer转换
    switchImgList() {
      return (list) => {
        let imgList = list
        imgList.forEach(item => {
          item._fieldValue = this.imgPrefix + item.fieldValue
          // item._fieldValue = 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg' // 测试
        })
        return imgList
      }
    }
  },
  created() {
    this.listItemDetail = this.stuDetail
    this.stuId = this.stuDetail.studentBaseId
    this.adSchool = this.stuDetail.enrollSchoolName
  },
  mounted() {
    this.getAdDetail();
  },
  methods: {
    // 获取报名字段
    getAdDetail() {
      adFormDetail({
        key: this.stuId
      }, this.$store.getters.deptCode).then(res => {
        // 除了海港区以外的逻辑
        const SPECIAL_DISTRICT_CODES = ['130274','130272'];
        if (!SPECIAL_DISTRICT_CODES.includes(this.$store.getters.deptCode) ) {
          let {followWorkType,liveForm,entitledGroupForm,enrollMiddleFieldVos} = res
          // 处理丰南区，优抚类型变化
          if (this.$store.getters.deptCode === '130207') {
            if (res && res.length > 0) {
              if (liveForm) {
                this.liveForm = liveForm;
                this.liveCert = liveForm.liveOption.liveCert;
              }
            }
          }
          // 处理随迁类型回显问题
          if (entitledGroupForm) {
            let {fields,value} = entitledGroupForm;
            this.entitledGroupForm = {
              value,
              fields: JSON.parse(fields)
            }
            this.entitledGroupFlag = true;
          }
          // res 等于之前的数据
          res = enrollMiddleFieldVos;

          // 按typeConfigId从小到大排序
          let resCopy = res.sort((a, b) => a.typeConfigId - b.typeConfigId);
          // 取基础信息里的学生身份证
          this.stuIdCard = resCopy.find(v => v.typeConfigId == 1).leafFieldInfos.find(v => v.fieldId == 3).fieldValue
          // 分类
          this.originList = resCopy.map(this.separateImgAndNormal)
          console.log("日志信息",JSON.stringify(this.originList,null,2))
          // 非房产非双胞胎的普通字段，typeConfigId为1，2，4，5，6，7
          let normalIdList = [1, 2, 4, 5, 6, 7, 20, 21].map(v => `${ v }`)
          this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1)
          // this.normalForm = this.originList.filter(v => v.typeConfigId < 8 && v.typeConfigId != 3 || v.typeConfigId === 20)
          // 房产字段：typeConfigId >= 8但小于19
          this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 && v.typeConfigId < 18)
          // 双胞胎字段：typeConfigId为3和19
          this.siblings.list = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
          // 其他补充信息
          this.others.list = this.originList.filter(v => v.typeConfigId == 18)

          // 经商或者务工只二选一，【6：务工信息，5: 经商信息】
          if (followWorkType && followWorkType === 5) {
            this.normalForm = this.normalForm.filter(item => item.typeConfigId !== "6")
          }else if (followWorkType && followWorkType === 6) {
            this.normalForm = this.normalForm.filter(item => item.typeConfigId !== "5")
          }
          // 加载完成
          this.loadingAdForm = false
        }else{   // 海港区
          // 按typeConfigId从小到大排序
          let resCopy = res.sort((a, b) => a.typeConfigId - b.typeConfigId);
          // 取基础信息里的学生身份证
          this.stuIdCard = resCopy.find(v => v.typeConfigId == 1).leafFieldInfos.find(v => v.fieldId == 3).fieldValue
          // 分类
          this.originList = resCopy.map(this.separateImgAndNormal)
          // 非房产非双胞胎的普通字段，typeConfigId为1，2，4，5，6，7
          this.normalForm = this.originList.filter(v => v.typeConfigId < 8 && v.typeConfigId != 3)
          // 房产字段：typeConfigId >= 8但小于19
          this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 && v.typeConfigId < 18)
          // 双胞胎字段：typeConfigId为3和19
          this.siblings.list = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
          // 其他补充信息
          this.others.list = this.originList.filter(v => v.typeConfigId == 18)
          // 加载完成
          this.loadingAdForm = false
        }

      })
    },
    // 字段通用处理：区分图片与非图片字段
    separateImgAndNormal(item) {
      // 非图片字段
      item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
      // 图片字段
      item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2 && fi.fieldValue)
      return item
    }
  }
}
</script>

<style lang="scss" scoped>
.enroll-detail {
  .common-title {
    margin-bottom: 10px;
  }
}

.form-group {
  margin: 0;

  .f-group-title {
    margin-bottom: 20px;
    padding: 0 15px;
    line-height: 46px;
    border-radius: 5px;
    background-color: #F1F1F1;
  }

  .f-group-title-pp {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .f-group-detail {
    margin: 0;
    margin-bottom: 30px;

    .f-g-d-info-list, .f-g-d-img-list {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
    }

    .f-g-d-info-list {
      padding: 0 10px;
    }

    .normal-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex: 0 0 33.3333%;
      margin-bottom: 10px;
    }

    .img-item {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      flex: 0 0 14.2857%;
      margin: 15px 0;

      .desc-txt {
        margin-top: 10px;
      }
    }
  }
}
.entitled-group-list {
  // 使内容横向排列（标签在左，内容在右）
  .entitled-item {
    display: flex;
    align-items: center;

    .desc-txt {
      margin-right: 8px;  // 标签和内容之间的间距
      min-width: 80px;    // 固定标签宽度（可选）
    }

    .res-txt {
      flex: 1;            // 内容区域自动填充剩余空间
    }
  }

  // 如果希望保持和其他表单相同的网格布局
  .normal-item {
    flex: 0 0 33.3333%;   // 每行显示3项
    margin-bottom: 10px;
  }
}
</style>
