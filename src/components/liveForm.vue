<template>
  <view>
    <dl class="form-group">
      <dt class="f-group-title">居住证信息</dt>
      <dd class="f-group-detail">
        <ul class="f-g-d-info-list">
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">房产类型：</div>
            <div class="res-txt" v-if="liveForm.liveType === 0 ">唐山各区户籍</div>
            <div class="res-txt" v-else>唐山各县及外省市户籍</div>
          </li>
        </ul>

        <ul class="f-g-d-info-list" v-if="liveForm.liveType===1">
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">居住证编号：</div>
            <div class="res-txt">{{liveForm.liveOption.code}}</div>
          </li>
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">居住证地址：</div>
            <div class="res-txt">{{liveForm.liveOption.address}}</div>
          </li>
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">居住证持有者：</div>
            <div class="res-txt">{{liveForm.liveOption.holder}}</div>
          </li>
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">持有者身份证号：</div>
            <div class="res-txt">{{liveForm.liveOption.idCard}}</div>
          </li>
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">与学生关系：</div>
            <div class="res-txt">{{liveForm.liveOption.relation}}</div>
          </li>
        </ul>

        <ul class="f-g-d-info-list">
          <li  class="f-g-d-item normal-item">
            <div class="desc-txt">房产类型：</div>
            <normal-img-ex :item-config="{'fieldId':'liveCert','fieldValue': liveCert}"></normal-img-ex>
          </li>
        </ul>
      </dd>
    </dl>
  </view>
</template>

<script>
import NormalImgEx from '@/components/Exhibition/NormalImgEx'

export default {
  components: {
    NormalImgEx
  },
  name: 'liveForm',
  props: {
    liveForm: {
      required: true,
      type: Object
    }
  },
  data() {
    return {}
  },
  mounted() {
    console.log(" this.liveInfo ==== > ", this.liveInfo)
  }

}

</script>


<style lang="scss">

</style>