import request from '@/utils/request';

// 获取区县
export const getDepts = (data) => request.post('/user-api/center/dept/list', data)

export const getDeptss = (data) => request.post('/user-api/center/dept/schoolPageList', data)
// 列表
export const getUserList = (data) => request.post('/user-api/center/user/list', data)
// 新增
export const create = (data) => request.post('/user-api/center/user/create', data)
// 编辑
export const update = (data) => request.post('/user-api/center/user/update', data)
// 详情
export const detail = (data) => request.post('/user-api/center/user/detail', data)
// 重置密码
export const resetPwd = (data) => request.post('/user-api/center/user/changePasswordDefault', data)
// 启用
export const enableUser = (data) => request.post('/user-api/center/user/changeStatusNormal', data)
// 禁用
export const disableUser = (data) => request.post('/user-api/center/user/changeStatusDisable', data)
// 删除
export const delUser = (data) => request.post('/user-api/center/user/delete', data)
// 分配权限详情
export const getUserAuditDetail = (data) => request.post('/user-api/center/user/getUserAuditDetail', data)
// 分配权限提交
export const auditSchool = (data) => request.post('/user-api/center/user/auditSchool', data)
// 获取审核账号分页数据
export const auditUserList = (data) => request.post('/user-api/center/user/auditUserList', data)
// 批量导入
export const importUser = data => request.post('/user-api/center/user/importUser', data)
export const importUsers = data => request.post('/user-api/center/user/importUsers', data)