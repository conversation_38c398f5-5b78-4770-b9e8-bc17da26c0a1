import request from '@/utils/request';
import { pref } from '@/utils/common';

// 非适龄儿童报名信息 - 列表
export const nonProperAgeStuList = (data, code) => request.post(`${pref + code}/biz/NonSchoolAge/getPage`, data)

// 非适龄儿童报名信息 - 新增
export const addNonProperAgeStu = (data, code) => request.post(`${pref + code}/biz/NonSchoolAge/add`, data)

// 非适龄儿童报名信息 - 删除
export const delNonProperAgeStu = (data, code) => request.post(`${pref + code}/biz/NonSchoolAge/delete`, data)